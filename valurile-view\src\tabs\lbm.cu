// Domain size
#define NX 256
#define NY 256

// D2Q9
static __constant__ int d_cx[9] = {0, 1, -1, 0, 0, 1, -1, 1, -1};
static __constant__ int d_cy[9] = {0, 0, 0, 1, -1, 1, 1, -1, -1};
static __constant__ float d_w[9] = {4.f / 9.f,  1.f / 9.f,  1.f / 9.f,
                                    1.f / 9.f,  1.f / 9.f,  1.f / 36.f,
                                    1.f / 36.f, 1.f / 36.f, 1.f / 36.f};
static __constant__ int iOpp[9] = {0, 2, 1, 4, 3,
                                   6, 5, 8, 7}; // bounce-back pairs

// simple helper: periodic or clamp
__device__ __forceinline__ int idx2D(int x, int y) {
  // for simplicity, use periodic:
  x = (x + NX) % NX;
  y = (y + NY) % NY;
  return x + y * NX;
}

__device__ bool isInsideCylinder(int x, int y, float R) {
  float dx = (float)x - 0.5f * (float)NX;
  float dy = (float)y - 0.5f * (float)NY;
  return ((dx * dx + dy * dy) <= R * R);
}

// Single-array timeslots "fi" shape: 2*(Q*NX*NY)
__device__ float load_f(const float *__restrict__ fi, int x, int y, int i,
                        int t_parity) {
  size_t base = (size_t)t_parity * 9ULL * NX * NY;
  size_t index = base + i * (NX * NY) + idx2D(x, y);
  return fi[index];
}
__device__ void store_f(float *__restrict__ fi, int x, int y, int i,
                        int t_parity, float val) {
  size_t base = (size_t)t_parity * 9ULL * NX * NY;
  size_t index = base + i * (NX * NY) + idx2D(x, y);
  fi[index] = val;
}

__device__ void computeEquilibrium(float rho, float ux, float uy, float *feq) {
  float uu = ux * ux + uy * uy;
  for (int i = 0; i < 9; i++) {
    float cu = d_cx[i] * ux + d_cy[i] * uy;
    feq[i] = d_w[i] * rho * (1.f + 3.f * cu + 4.5f * cu * cu - 1.5f * uu);
  }
}

//---------------------------------------------
// The main LBM kernel with Cylinder Bounceback
//---------------------------------------------
extern "C" __global__ void lbm_stream_collide_cylinder(float *__restrict__ fi,
                                            float *__restrict__ rho,
                                            float *__restrict__ ux,
                                            float *__restrict__ uy, float omega,
                                            float R, // cylinder radius
                                            int current_t_parity) {
  int x = blockIdx.x * blockDim.x + threadIdx.x;
  int y = blockIdx.y * blockDim.y + threadIdx.y;
  if (x >= NX || y >= NY)
    return;

  // next timeslot
  int next_parity = 1 - current_t_parity;

  // 1) "Pull" with bounce-back if neighbor is inside cylinder
  float f_in[9];
  for (int i = 0; i < 9; i++) {
    // neighbor coordinate
    int xn = x - d_cx[i];
    int yn = y - d_cy[i];

    // if neighbor is inside cylinder => bounce
    if (isInsideCylinder(xn, yn, R)) {
      // bounce-back: read from opposite direction in *our own cell*
      f_in[i] = load_f(fi, x, y, iOpp[i], current_t_parity);
    } else {
      // usual pull
      f_in[i] = load_f(fi, xn, yn, i, current_t_parity);
    }
  }

  // 2) Macroscopic fields
  float rh = 0.f;
  float vx = 0.f;
  float vy = 0.f;
  for (int i = 0; i < 9; i++) {
    rh += f_in[i];
    vx += f_in[i] * d_cx[i];
    vy += f_in[i] * d_cy[i];
  }
  rho[idx2D(x, y)] = rh;
  ux[idx2D(x, y)] = vx / rh;
  uy[idx2D(x, y)] = vy / rh;

  // 3) Collision (SRT)
  float feq[9];
  computeEquilibrium(rh, vx / rh, vy / rh, feq);
  for (int i = 0; i < 9; i++) {
    float fout = f_in[i] + omega * (feq[i] - f_in[i]);
    // store to next timeslot (push)
    store_f(fi, x, y, i, next_parity, fout);
  }
}
