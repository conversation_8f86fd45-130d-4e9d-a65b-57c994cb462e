use crate::tab_viewer::Tab<PERSON><PERSON><PERSON>;
use crate::theme;
use eframe::egui;
use eframe::App;
use egui_dock::{DockArea, DockState, Style};

#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, PartialEq)]
pub enum ThemeChoice {
    DarkBlue,
    LightBlue,
    Default,
}

pub struct DockingApp<T: 'static> {
    pub state: DockState<T>,
    pub theme_choice: ThemeChoice,
}

impl<T: Default + Clone + 'static> Default for DockingApp<T> {
    fn default() -> Self {
        let state = DockState::new(Vec::new());
        Self { 
            state,
            theme_choice: ThemeChoice::DarkBlue,
        }
    }
}

impl<T: 'static> DockingApp<T> {
    pub fn new<F>(initial_tabs: Vec<T>, splitter: Option<F>) -> Self
    where
        F: FnOnce(&mut DockState<T>),
    {
        let mut state = DockState::new(initial_tabs);
        if let Some(splitter) = splitter {
            splitter(&mut state);
        }
        Self { 
            state,
            theme_choice: ThemeChoice::DarkBlue,
        }
    }
}

impl<T: 'static + crate::tabs::DockTab> App for DockingApp<T> {
    fn update(&mut self, ctx: &egui::Context, _frame: &mut eframe::Frame) {
        // Apply the selected theme
        match self.theme_choice {
            ThemeChoice::DarkBlue => theme::apply_dark_blue_theme(ctx),
            ThemeChoice::LightBlue => theme::apply_light_blue_theme(ctx),
            ThemeChoice::Default => {
                // Use default egui dark theme
                ctx.set_visuals(egui::Visuals::dark());
            }
        }
        
        egui::TopBottomPanel::top("top_panel").show(ctx, |ui| {
            egui::menu::bar(ui, |ui| {
                // Theme selector
                ui.menu_button("Theme", |ui| {
                    ui.radio_value(&mut self.theme_choice, ThemeChoice::DarkBlue, "Dark Blue");
                    ui.radio_value(&mut self.theme_choice, ThemeChoice::LightBlue, "Light Blue");
                    ui.radio_value(&mut self.theme_choice, ThemeChoice::Default, "Default");
                });
                
                ui.separator();
                egui::widgets::global_theme_preference_switch(ui);

                let is_web = cfg!(target_arch = "wasm32");
                if !is_web {
                    ui.menu_button("File", |ui| {
                        if ui.button("Quit").clicked() {
                            std::process::exit(0);
                        }
                    });
                }
            });
        });
        egui::TopBottomPanel::bottom("bottom_panel").show(ctx, |ui| {
            ui.horizontal(|ui| {
                ui.label("Bottom panel");
                ui.add(egui::ProgressBar::new(0.5).animate(true));
            });
        });
        egui::SidePanel::left("left_panel").show(ctx, |ui| {
            ui.label("Left panel");
        });

        egui::Window::new("Log").show(ctx, |ui| {
            egui_logger::logger_ui().show(ui);
        });

        DockArea::new(&mut self.state)
            .style(Style::from_egui(ctx.style().as_ref()))
            .show(ctx, &mut TabViewer::<T>::default());
    }
}