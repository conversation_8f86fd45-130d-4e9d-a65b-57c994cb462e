// LBM Visualization Shader for wgpu
// Renders velocity magnitude data as a colorized visualization

struct VertexOutput {
    @builtin(position) clip_position: vec4<f32>,
    @location(0) tex_coords: vec2<f32>,
}

@vertex
fn vs_main(@builtin(vertex_index) vertex_index: u32) -> VertexOutput {
    var out: VertexOutput;
    
    // Generate fullscreen triangle
    let x = f32(i32(vertex_index) - 1);
    let y = f32(i32(vertex_index & 1u) * 2 - 1);
    
    out.clip_position = vec4<f32>(x, y, 0.0, 1.0);
    out.tex_coords = vec2<f32>((x + 1.0) * 0.5, (1.0 - y) * 0.5);
    
    return out;
}

@group(0) @binding(0)
var velocity_texture: texture_2d<f32>;

@group(0) @binding(1)
var velocity_sampler: sampler;

// Color mapping function - converts velocity magnitude to color
fn velocity_to_color(velocity: f32) -> vec3<f32> {
    let normalized_vel = clamp(velocity, 0.0, 1.0);
    
    // Create a rainbow color map
    let hue = (1.0 - normalized_vel) * 0.8; // Blue to red
    let saturation = 1.0;
    let value = 1.0;
    
    // HSV to RGB conversion
    let c = value * saturation;
    let x = c * (1.0 - abs((hue * 6.0) % 2.0 - 1.0));
    let m = value - c;
    
    var rgb: vec3<f32>;
    
    if (hue < 1.0/6.0) {
        rgb = vec3<f32>(c, x, 0.0);
    } else if (hue < 2.0/6.0) {
        rgb = vec3<f32>(x, c, 0.0);
    } else if (hue < 3.0/6.0) {
        rgb = vec3<f32>(0.0, c, x);
    } else if (hue < 4.0/6.0) {
        rgb = vec3<f32>(0.0, x, c);
    } else if (hue < 5.0/6.0) {
        rgb = vec3<f32>(x, 0.0, c);
    } else {
        rgb = vec3<f32>(c, 0.0, x);
    }
    
    return rgb + vec3<f32>(m);
}

@fragment
fn fs_main(in: VertexOutput) -> @location(0) vec4<f32> {
    // Sample velocity magnitude from texture
    let velocity_magnitude = textureSample(velocity_texture, velocity_sampler, in.tex_coords).r;
    
    // Convert to color
    let color = velocity_to_color(velocity_magnitude);
    
    return vec4<f32>(color, 1.0);
}

// Alternative fragment shader for debugging - shows raw texture data
@fragment
fn fs_debug(in: VertexOutput) -> @location(0) vec4<f32> {
    let velocity = textureSample(velocity_texture, velocity_sampler, in.tex_coords).r;
    return vec4<f32>(velocity, velocity, velocity, 1.0);
}

// Fragment shader with enhanced visualization features
@fragment
fn fs_enhanced(in: VertexOutput) -> @location(0) vec4<f32> {
    let velocity_magnitude = textureSample(velocity_texture, velocity_sampler, in.tex_coords).r;
    
    // Enhanced color mapping with multiple visualization modes
    var color: vec3<f32>;
    
    // Mode 1: Rainbow velocity mapping
    if (velocity_magnitude < 0.001) {
        // Very low velocity - dark blue
        color = vec3<f32>(0.0, 0.0, 0.2);
    } else if (velocity_magnitude < 0.1) {
        // Low velocity - blue to cyan
        let t = velocity_magnitude / 0.1;
        color = mix(vec3<f32>(0.0, 0.0, 0.5), vec3<f32>(0.0, 0.5, 1.0), t);
    } else if (velocity_magnitude < 0.3) {
        // Medium velocity - cyan to green
        let t = (velocity_magnitude - 0.1) / 0.2;
        color = mix(vec3<f32>(0.0, 0.5, 1.0), vec3<f32>(0.0, 1.0, 0.0), t);
    } else if (velocity_magnitude < 0.6) {
        // High velocity - green to yellow
        let t = (velocity_magnitude - 0.3) / 0.3;
        color = mix(vec3<f32>(0.0, 1.0, 0.0), vec3<f32>(1.0, 1.0, 0.0), t);
    } else if (velocity_magnitude < 0.8) {
        // Very high velocity - yellow to orange
        let t = (velocity_magnitude - 0.6) / 0.2;
        color = mix(vec3<f32>(1.0, 1.0, 0.0), vec3<f32>(1.0, 0.5, 0.0), t);
    } else {
        // Extreme velocity - orange to red
        let t = (velocity_magnitude - 0.8) / 0.2;
        color = mix(vec3<f32>(1.0, 0.5, 0.0), vec3<f32>(1.0, 0.0, 0.0), t);
    }
    
    // Add some contrast enhancement
    color = pow(color, vec3<f32>(0.8));
    
    return vec4<f32>(color, 1.0);
}

// Streamline visualization fragment shader
@fragment
fn fs_streamlines(in: VertexOutput) -> @location(0) vec4<f32> {
    let velocity = textureSample(velocity_texture, velocity_sampler, in.tex_coords).r;
    
    // Create streamline-like effect by sampling neighboring pixels
    let texel_size = 1.0 / vec2<f32>(textureDimensions(velocity_texture, 0));
    
    let v_right = textureSample(velocity_texture, velocity_sampler, in.tex_coords + vec2<f32>(texel_size.x, 0.0)).r;
    let v_up = textureSample(velocity_texture, velocity_sampler, in.tex_coords + vec2<f32>(0.0, texel_size.y)).r;
    
    // Calculate gradient for flow direction visualization
    let gradient_x = v_right - velocity;
    let gradient_y = v_up - velocity;
    let gradient_magnitude = sqrt(gradient_x * gradient_x + gradient_y * gradient_y);
    
    // Combine velocity magnitude with gradient for streamline effect
    let combined = velocity + gradient_magnitude * 0.5;
    let color = velocity_to_color(combined);
    
    return vec4<f32>(color, 1.0);
}
