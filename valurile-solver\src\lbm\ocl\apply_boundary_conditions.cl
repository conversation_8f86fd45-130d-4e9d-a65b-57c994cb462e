// apply_boundary_conditions.cl

#pragma OPENCL EXTENSION cl_khr_fp64 : enable

__kernel void apply_boundary_conditions(
    __global double* f,
    __global double* solidity,
    __constant int* cxs,
    __constant int* cys,
    double inlet_velocity,
    int nx,
    int ny,
    int nl
) {
    int y = get_global_id(0);
    int x = get_global_id(1);
    if (y >= ny || x >= nx) return;

    int idx = y * nx + x;
    int f_idx = idx * nl;

    // Cylinder Boundary
    if (solidity[idx] > 0.0) {
        double temp[9];
        for (int i = 0; i < nl; i++) {
            temp[i] = f[f_idx + i];
        }
        f[f_idx + 1] = temp[3];
        f[f_idx + 2] = temp[4];
        f[f_idx + 3] = temp[1];
        f[f_idx + 4] = temp[2];
        f[f_idx + 5] = temp[7];
        f[f_idx + 6] = temp[8];
        f[f_idx + 7] = temp[5];
        f[f_idx + 8] = temp[6];
    }

    // Inlet Boundary
    if (x == 0) {
        double rho = f[f_idx + 0] + f[f_idx + 2] + f[f_idx + 4] + 2.0 * (f[f_idx + 1] + f[f_idx + 5] + f[f_idx + 8]);
        f[f_idx + 3] = f[f_idx + 1] - (2.0 / 3.0) * rho * inlet_velocity;
        f[f_idx + 7] = f[f_idx + 5] - (rho * inlet_velocity) / 6.0;
        f[f_idx + 6] = f[f_idx + 8] - (rho * inlet_velocity) / 6.0;
    }

    // Outlet Boundary
    if (x == nx - 1) {
        int prev_idx = (y * nx + x - 1) * nl;
        for (int i = 0; i < nl; i++) {
            f[f_idx + i] = f[prev_idx + i];
        }
    }
}

