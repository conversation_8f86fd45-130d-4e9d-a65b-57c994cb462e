// collision_and_streaming.cl

#pragma OPENCL EXTENSION cl_khr_fp64 : enable

__kernel void collision_and_streaming(
    __global double* f_in,
    __global double* f_out,
    __global double* solidity,
    __constant int* cxs,
    __constant int* cys,
    __constant double* weights,
    double tau,
    int nx,
    int ny,
    int nl
) {
    int y = get_global_id(0);
    int x = get_global_id(1);
    if (y >= ny || x >= nx) return;

    int idx = y * nx + x;
    int f_idx = idx * nl;

    double f_local[9];
    for (int i = 0; i < nl; i++) {
        f_local[i] = f_in[f_idx + i];
    }

    // Compute macroscopic variables (rho, ux, uy) for collision
    double rho_val = 0.0;
    double ux_val = 0.0;
    double uy_val = 0.0;
    for (int i = 0; i < nl; i++) {
        double f_i = f_local[i];
        rho_val += f_i;
        ux_val += f_i * cxs[i];
        uy_val += f_i * cys[i];
    }

    // Avoid division by zero
    if (rho_val < 1e-8) {
        rho_val = 1e-8;
    }

    ux_val /= rho_val;
    uy_val /= rho_val;

    // No-slip condition inside the cylinder for collision
    if (solidity[idx] > 0.0) {
        ux_val = 0.0;
        uy_val = 0.0;
    }

    double u_square = ux_val * ux_val + uy_val * uy_val;

    // Collision step
    for (int i = 0; i < nl; i++) {
        double cu = cxs[i] * ux_val + cys[i] * uy_val;
        double feq = rho_val * weights[i] * (1.0 + 3.0 * cu + 4.5 * cu * cu - 1.5 * u_square);
        f_local[i] += (feq - f_local[i]) / tau;

        // Safeguard
        if (f_local[i] < 0.0) {
            f_local[i] = 1e-8;
        }
    }

    // Streaming step
    for (int i = 0; i < nl; i++) {
        int cy = cys[i];
        int cx = cxs[i];
        int new_y = (y + cy + ny) % ny;
        int new_x = (x + cx + nx) % nx;
        int dst_idx = (new_y * nx + new_x) * nl + i;
        f_out[dst_idx] = f_local[i];
    }
}

