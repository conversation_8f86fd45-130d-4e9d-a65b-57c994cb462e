use eframe::egui;
use eframe::egui_glow;
use egui::mutex::Mutex;
use egui_glow::glow;
use glow::HasContext as _;
use std::sync::atomic::{AtomicBool, Ordering};
use std::sync::Arc;

use valurile_solver::lbm::data::{LBMParams, SimulationState};
use valurile_solver::lbm::parallel_solver::{
    apply_boundary_conditions, calculate_macroscopic_variables, initialize_simulation_state,
    perform_collision_step, perform_streaming_step,
};

pub struct MySim {
    pub state: Mutex<SimulationState>,
    pub params: LBMParams,
    pub iteration: Mutex<usize>,
}

impl MySim {
    pub fn new(params: LBMParams) -> Self {
        let state = initialize_simulation_state(&params);
        Self {
            state: Mutex::new(state),
            iteration: Mutex::new(0),
            params,
        }
    }
}

pub struct LBMVelocityTab {
    gl_objects: Option<Arc<Mutex<VelocityColorBuffer>>>,
    gl: Option<Arc<glow::Context>>,
    sim: Arc<MySim>,
    running: Arc<AtomicBool>,
}

impl LBMVelocityTab {
    pub fn new(gl: &Arc<glow::Context>, params: LBMParams) -> Self {
        let sim = Arc::new(MySim::new(params));

        let width = sim.params.nx as u32;
        let height = sim.params.ny as u32;

        let gl_objects =
            VelocityColorBuffer::new(gl, width, height).map(|b| Arc::new(Mutex::new(b)));
        let running = Arc::new(AtomicBool::new(false));

        Self {
            gl_objects,
            gl: Some(gl.clone()),
            sim,
            running,
        }
    }
}

// Implement the DockTab trait so it appears in your UI
impl crate::tabs::DockTab for LBMVelocityTab {
    fn title(&self) -> &str {
        "LBM Velocity Visualization"
    }

    fn ui(&mut self, ui: &mut egui::Ui) {
        if let (Some(_gl_arc), Some(interop_arc)) = (&self.gl, &self.gl_objects) {
            ui.horizontal(|ui| {
                let running = self.running.clone();
                let mut temp = running.load(Ordering::SeqCst);
                if ui.checkbox(&mut temp, "Run Simulation").changed() {
                    running.store(temp, Ordering::SeqCst);
                }
            });

            let (rect, _resp) =
                ui.allocate_exact_size(egui::vec2(600.0, 600.0), egui::Sense::hover());

            let buffer_clone = interop_arc.clone();
            let sim_clone = self.sim.clone();
            let running_clone = self.running.clone(); // Clone the running flag

            let cb = egui_glow::CallbackFn::new(move |_info, painter| {
                let gl = painter.gl();
                let mut color_buf = buffer_clone.lock();

                // Check if the simulation is running
                if running_clone.load(Ordering::SeqCst) {
                    // Lock state and iteration separately
                    let mut state = sim_clone.state.lock();
                    let params = &sim_clone.params;
                    let mut iteration = sim_clone.iteration.lock();
                    let solidity = state.solidity.clone(); // Cloning to avoid borrow issues

                    perform_collision_step(&mut state, params);
                    perform_streaming_step(&mut state.f, params);
                    apply_boundary_conditions(&mut state.f, &solidity, params);
                    calculate_macroscopic_variables(&mut state, params);
                    *iteration += 1;

                    color_buf.update_velocity_map(gl, &state);
                }

                color_buf.paint(gl);
            });

            ui.painter().add(egui::PaintCallback {
                rect,
                callback: Arc::new(cb),
            });
        }
    }

    fn closeable(&mut self) -> bool {
        false
    }
}

// If you want a Drop, same pattern as your checkerboard code
impl Drop for LBMVelocityTab {
    fn drop(&mut self) {
        if let (Some(gl), Some(interop)) = (&self.gl, &self.gl_objects) {
            interop.lock().destroy(&gl);
        }
    }
}

// A struct that manages the PBO, texture, shaders, and quad geometry
pub struct VelocityColorBuffer {
    pbo: glow::Buffer,
    texture: glow::Texture,
    program: glow::Program,
    vertex_array: glow::VertexArray,
    vbo: glow::Buffer,
    ebo: glow::Buffer,
    width: u32,
    height: u32,
}

unsafe impl Send for VelocityColorBuffer {}
unsafe impl Sync for VelocityColorBuffer {}

impl VelocityColorBuffer {
    /// Create the GPU objects needed for a single float channel texture
    pub fn new(gl: &glow::Context, width: u32, height: u32) -> Option<Self> {
        unsafe {
            let pbo = gl.create_buffer().ok()?;
            gl.bind_buffer(glow::PIXEL_UNPACK_BUFFER, Some(pbo));

            let size_in_bytes = (width * height * std::mem::size_of::<f32>() as u32) as i32;
            gl.buffer_data_size(glow::PIXEL_UNPACK_BUFFER, size_in_bytes, glow::DYNAMIC_DRAW);
            gl.bind_buffer(glow::PIXEL_UNPACK_BUFFER, None);

            let texture = gl.create_texture().ok()?;
            gl.bind_texture(glow::TEXTURE_2D, Some(texture));
            gl.tex_image_2d(
                glow::TEXTURE_2D,
                0,
                glow::R32F as i32, // store 1 float per texel
                width as i32,
                height as i32,
                0,
                glow::RED,
                glow::FLOAT,
                glow::PixelUnpackData::Slice(None),
            );

            gl.tex_parameter_i32(
                glow::TEXTURE_2D,
                glow::TEXTURE_MIN_FILTER,
                glow::NEAREST as i32,
            );
            gl.tex_parameter_i32(
                glow::TEXTURE_2D,
                glow::TEXTURE_MAG_FILTER,
                glow::NEAREST as i32,
            );
            gl.bind_texture(glow::TEXTURE_2D, None);

            let program = gl.create_program().ok()?;
            let vs_src = r#"#version 330
                layout(location=0) in vec2 a_pos;
                layout(location=1) in vec2 a_uv;
                out vec2 v_uv;
                void main() {
                    v_uv = a_uv;
                    gl_Position = vec4(a_pos, 0.0, 1.0);
                }
            "#;

            let fs_src = r#"#version 330
                in vec2 v_uv;
                out vec4 out_color;
                uniform sampler2D u_texture;

                void main() {
                    float val = texture(u_texture, v_uv).r;
                    vec3 color = mix(vec3(0.0, 0.0, 1.0),
                                     vec3(1.0, 0.0, 0.0),
                                     val);
                    out_color = vec4(color, 1.0);
                }
            "#;

            let vs = gl.create_shader(glow::VERTEX_SHADER).unwrap();
            gl.shader_source(vs, vs_src);
            gl.compile_shader(vs);
            if !gl.get_shader_compile_status(vs) {
                eprintln!("Vertex shader error: {}", gl.get_shader_info_log(vs));
                return None;
            }

            let fs = gl.create_shader(glow::FRAGMENT_SHADER).unwrap();
            gl.shader_source(fs, fs_src);
            gl.compile_shader(fs);
            if !gl.get_shader_compile_status(fs) {
                eprintln!("Fragment shader error: {}", gl.get_shader_info_log(fs));
                return None;
            }

            gl.attach_shader(program, vs);
            gl.attach_shader(program, fs);
            gl.link_program(program);
            if !gl.get_program_link_status(program) {
                eprintln!("Program link error: {}", gl.get_program_info_log(program));
                return None;
            }
            gl.detach_shader(program, vs);
            gl.detach_shader(program, fs);
            gl.delete_shader(vs);
            gl.delete_shader(fs);

            let vertex_array = gl.create_vertex_array().ok()?;
            let vbo = gl.create_buffer().ok()?;
            let ebo = gl.create_buffer().ok()?;

            gl.bind_vertex_array(Some(vertex_array));

            let vertex_data: [f32; 16] = [
                -1.0, -1.0, 0.0, 0.0, 1.0, -1.0, 1.0, 0.0, 1.0, 1.0, 1.0, 1.0, -1.0, 1.0, 0.0, 1.0,
            ];
            let indices: [u32; 6] = [0, 1, 2, 0, 2, 3];

            gl.bind_buffer(glow::ARRAY_BUFFER, Some(vbo));
            gl.buffer_data_u8_slice(
                glow::ARRAY_BUFFER,
                bytemuck::cast_slice(&vertex_data),
                glow::STATIC_DRAW,
            );

            gl.bind_buffer(glow::ELEMENT_ARRAY_BUFFER, Some(ebo));
            gl.buffer_data_u8_slice(
                glow::ELEMENT_ARRAY_BUFFER,
                bytemuck::cast_slice(&indices),
                glow::STATIC_DRAW,
            );

            gl.enable_vertex_attrib_array(0);
            gl.vertex_attrib_pointer_f32(0, 2, glow::FLOAT, false, 4 * 4, 0);

            gl.enable_vertex_attrib_array(1);
            let uv_offset = 2 * std::mem::size_of::<f32>() as i32;
            gl.vertex_attrib_pointer_f32(1, 2, glow::FLOAT, false, 4 * 4, uv_offset);

            gl.bind_vertex_array(None);

            Some(Self {
                pbo,
                texture,
                program,
                vertex_array,
                vbo,
                ebo,
                width,
                height,
            })
        }
    }

    /// Upload the current LBM velocity magnitude to the GPU.
    /// `state.ux` and `state.uy` are shape: (ny, nx)
    pub fn update_velocity_map(&mut self, gl: &glow::Context, state: &SimulationState) {
        let nx = self.width as usize;
        let ny = self.height as usize;

        let mut data = vec![0.0f32; nx * ny];

        let mut max_speed = 1e-6;
        for y in 0..ny {
            for x in 0..nx {
                let vx = state.ux[[y, x]];
                let vy = state.uy[[y, x]];
                let speed = (vx * vx + vy * vy).sqrt();
                if speed > max_speed {
                    max_speed = speed;
                }
            }
        }

        for y in 0..ny {
            for x in 0..nx {
                let vx = state.ux[[y, x]];
                let vy = state.uy[[y, x]];
                let speed = (vx * vx + vy * vy).sqrt() as f32;
                data[y * nx + x] = (speed / max_speed as f32).min(1.0);
            }
        }

        unsafe {
            gl.bind_buffer(glow::PIXEL_UNPACK_BUFFER, Some(self.pbo));

            gl.buffer_data_u8_slice(
                glow::PIXEL_UNPACK_BUFFER,
                bytemuck::cast_slice(&data),
                glow::DYNAMIC_DRAW,
            );

            gl.bind_texture(glow::TEXTURE_2D, Some(self.texture));
            gl.tex_sub_image_2d(
                glow::TEXTURE_2D,
                0,
                0,
                0,
                self.width as i32,
                self.height as i32,
                glow::RED,
                glow::FLOAT,
                glow::PixelUnpackData::BufferOffset(0),
            );

            gl.bind_buffer(glow::PIXEL_UNPACK_BUFFER, None);
        }
    }

    /// Draw the quad
    pub fn paint(&self, gl: &glow::Context) {
        unsafe {
            gl.use_program(Some(self.program));
            gl.active_texture(glow::TEXTURE0);
            gl.bind_texture(glow::TEXTURE_2D, Some(self.texture));
            if let Some(loc) = gl.get_uniform_location(self.program, "u_texture") {
                gl.uniform_1_i32(Some(&loc), 0);
            }
            gl.bind_vertex_array(Some(self.vertex_array));
            gl.draw_elements(glow::TRIANGLES, 6, glow::UNSIGNED_INT, 0);
        }
    }

    pub fn destroy(&self, gl: &glow::Context) {
        unsafe {
            gl.delete_buffer(self.pbo);
            gl.delete_texture(self.texture);
            gl.delete_program(self.program);
            gl.delete_buffer(self.vbo);
            gl.delete_buffer(self.ebo);
            gl.delete_vertex_array(self.vertex_array);
        }
    }
}
