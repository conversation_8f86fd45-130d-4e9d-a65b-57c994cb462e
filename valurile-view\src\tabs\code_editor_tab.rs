use crate::tabs::DockTab;
use eframe::egui;
use egui_code_editor::{CodeEditor, ColorTheme, Syntax};

pub struct CodeEditorTab {
    pub title: String,
    pub code: String,
}

impl DockTab for CodeEditorTab {
    fn title(&self) -> &str {
        &self.title
    }
    fn ui(&mut self, ui: &mut egui::Ui) {
        ui.heading("Code Editor");
        CodeEditor::default()
            .id_source("code editor")
            .with_rows(12)
            .with_fontsize(14.0)
            .with_theme(ColorTheme::GRUVBOX)
            .with_syntax(Syntax::python())
            .with_numlines(true)
            .show(ui, &mut self.code);
    }
    fn closeable(&mut self) -> bool {
        false
    }
}
