[workspace]
members = [ "valurile-cli",
    "valurile-core",
    "valurile-solver",
    "valurile-view",
]

resolver = "2"

[workspace.dependencies]
cudarc = { version = "0.16.4", default-features = false, features = ["std", "cuda-12060", "driver", "nvrtc", "dynamic-linking"] }
glow = "0.16.0"
clap = { version = "4.5.38", features = ["derive"] }
eframe = "0.31.1"
egui = "0.31.1"
egui_glow = "0.31.1"
bytemuck = "1.21.0"

# Enable more optimization in the release profile at the cost of compile time.
[profile.release]
# Compile the entire crate as one unit.
# Slows compile times, marginal improvements.
# codegen-units = 1
# Do a second optimization pass over the entire program, including dependencies.
# Slows compile times, marginal improvements.
# lto = "thin"

# Optimize for size in the wasm-release profile to reduce load times and bandwidth usage on web.
[profile.wasm-release]
# Default to release profile values.
inherits = "release"
# Optimize with size in mind (also try "z", sometimes it is better).
# Slightly slows compile times, great improvements to file size and runtime performance.
opt-level = "s"
# Strip all debugging information from the binary to slightly reduce file size.
strip = "debuginfo"
