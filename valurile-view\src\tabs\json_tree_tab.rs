use crate::tabs::DockTab;
use eframe::egui;
use egui_json_tree::JsonTree;
use serde_json::Value;

pub struct JsonTreeTab {
    pub title: String,
    pub json: Value,
}

impl DockTab for JsonTreeTab {
    fn title(&self) -> &str {
        &self.title
    }
    fn ui(&mut self, ui: &mut egui::Ui) {
        ui.heading("JSON Tree");
        JsonTree::new("json-tree", &self.json).show(ui);
    }
    fn closeable(&mut self) -> bool {
        false
    }
}
