use ndarray::{s, <PERSON>rray1, <PERSON>rray2, <PERSON><PERSON>y3, <PERSON>, Zip};
use ndarray_rand::{rand_distr::Normal, RandomExt};

use rand::rngs::StdRng;
use rand::SeedableRng;

use std::time::Instant;

use super::data::*;
use super::util::*;

pub fn parallel_solve(params: &LBMParams) {
    assert!(
        params.tau > 0.5,
        "Relaxation time tau must be greater than 0.5 for stability."
    );
    println!("Starting Multi-threaded Solver");
    println!("Starting LBM Simulation...");
    // dbg!(&params);

    let mut state = initialize_simulation_state(params);
    let reynolds_number = calculate_reynolds_number(params);
    println!("Reynolds number: {}", reynolds_number as usize);

    // Initialize cumulative timers
    let mut total_collision_time = std::time::Duration::ZERO;
    let mut total_streaming_time = std::time::Duration::ZERO;
    let mut total_boundary_time = std::time::Duration::ZERO;
    let mut total_macroscopic_time = std::time::Duration::ZERO;
    let mut total_validation_time = std::time::Duration::ZERO;

    for it in 0..params.nt {
        if (it + 1) % 50 == 0 {
            println!("Iteration: {}", it + 1);
        }

        // 1. Collision Step
        let collision_start = Instant::now();
        perform_collision_step(&mut state, params);
        total_collision_time += collision_start.elapsed();

        // 2. Streaming Step
        let streaming_start = Instant::now();
        perform_streaming_step(&mut state.f, params);
        total_streaming_time += streaming_start.elapsed();

        // 3. Apply Boundary Conditions
        let boundary_start = Instant::now();
        apply_boundary_conditions(&mut state.f, &state.solidity, params);
        total_boundary_time += boundary_start.elapsed();

        // 4. Calculate Macroscopic Variables
        let macroscopic_start = Instant::now();
        calculate_macroscopic_variables(&mut state, params);
        total_macroscopic_time += macroscopic_start.elapsed();

        // 5. Validate Simulation State
        let validation_start = Instant::now();
        validate_simulation_state(&state, params, it);
        total_validation_time += validation_start.elapsed();

        // 6. Export Results Periodically
        if (it + 1) % 50 == 0 {
            export_results(&state, params, it);
        }
    }

    // After simulation, print average timings
    println!(
        "Average Collision step time: {:?}",
        total_collision_time / params.nt as u32
    );
    println!(
        "Average Streaming step time: {:?}",
        total_streaming_time / params.nt as u32
    );
    println!(
        "Average Boundary conditions time: {:?}",
        total_boundary_time / params.nt as u32
    );
    println!(
        "Average Macroscopic variables calculation time: {:?}",
        total_macroscopic_time / params.nt as u32
    );
    println!(
        "Average Validation step time: {:?}",
        total_validation_time / params.nt as u32
    );
}

pub fn initialize_simulation_state(params: &LBMParams) -> SimulationState {
    let mut rng = StdRng::seed_from_u64(42);
    let mut f = Array3::<f64>::zeros((params.ny, params.nx, params.nl));

    // 1. Initialize equilibrium distributions
    for y in 0..params.ny {
        for x in 0..params.nx {
            for i in 0..params.nl {
                f[[y, x, i]] = params.weights[i] * params.rho0;
            }
        }
    }

    // 2. Add small perturbations to avoid symmetry
    f = &f
        + &Array3::random_using(
            (params.ny, params.nx, params.nl),
            Normal::new(0.0, 0.001).unwrap(),
            &mut rng,
        );

    // 3. Apply inlet velocity once
    for y in 0..params.ny {
        f[[y, 0, 1]] += params.weights[1] * 3.0 * params.inlet_velocity;
        f[[y, 0, 5]] += params.weights[5] * 3.0 * params.inlet_velocity;
        f[[y, 0, 8]] += params.weights[8] * 3.0 * params.inlet_velocity;
    }

    // 4. Scale to maintain rho0
    let rho = f.sum_axis(Axis(2));
    for i in 0..params.nl {
        Zip::from(f.slice_mut(s![.., .., i]))
            .and(&rho)
            .for_each(|f_i, &rho_xy| {
                *f_i *= params.rho0 / rho_xy;
                // Prevent negative or non-finite f's
                if *f_i < 0.0 || !f_i.is_finite() {
                    *f_i = 1e-8;
                }
            });
    }

    // Initialize cylinder solidity
    let solidity = initialize_solidity(params);

    SimulationState {
        f,
        rho,
        ux: Array2::<f64>::zeros((params.ny, params.nx)),
        uy: Array2::<f64>::zeros((params.ny, params.nx)),
        solidity,
    }
}

fn initialize_solidity(params: &LBMParams) -> Array2<f64> {
    let mut solidity = Array2::<f64>::zeros((params.ny, params.nx));
    let cylinder_diameter = params.ny as f64 / 20.0;
    for y in 0..params.ny {
        for x in 0..params.nx {
            if ((x as f64 - params.nx as f64 / 3.0).powi(2)
                + (y as f64 - params.ny as f64 / 2.0).powi(2))
                < (cylinder_diameter / 2.0).powi(2)
            {
                solidity[[y, x]] = 1.0;
            }
        }
    }
    solidity
}

pub fn perform_streaming_step(f: &mut Array3<f64>, params: &LBMParams) {
    let mut f_new = Array3::<f64>::zeros(f.dim());
    let nx = params.nx;
    let ny = params.ny;

    Zip::indexed(&mut f_new).par_for_each(|(y, x, i), f_new_value| {
        let cy = params.cys[i].rem_euclid(ny as i32) as usize;
        let cx = params.cxs[i].rem_euclid(nx as i32) as usize;

        let new_y = (y + ny - cy) % ny;
        let new_x = (x + nx - cx) % nx;

        *f_new_value = f[[new_y, new_x, i]];
    });

    std::mem::swap(f, &mut f_new);
}

pub fn calculate_macroscopic_variables(state: &mut SimulationState, params: &LBMParams) {
    // Sum over the distribution functions to get density
    state.rho = state.f.sum_axis(Axis(2));

    // Prevent division by zero by setting a minimum density
    state
        .rho
        .mapv_inplace(|rho_val| if rho_val < 1e-8 { 1e-8 } else { rho_val });

    // Compute velocities using broadcasting and sum over the last axis
    let cxs = Array1::from_iter(params.cxs.iter().map(|&c| c as f64));
    let cys = Array1::from_iter(params.cys.iter().map(|&c| c as f64));

    state.ux = (&state.f * &cxs).sum_axis(Axis(2));
    state.uy = (&state.f * &cys).sum_axis(Axis(2));

    // Check for NaN or infinite velocities before division
    // if state.ux.iter().any(|&u| !u.is_finite()) || state.uy.iter().any(|&u| !u.is_finite()) {
    //     panic!("Non-finite velocities detected before division!");
    // }

    // Divide by density to get velocity
    Zip::from(&mut state.ux)
        .and(&state.rho)
        .par_for_each(|u, &r| *u /= r);
    Zip::from(&mut state.uy)
        .and(&state.rho)
        .par_for_each(|u, &r| *u /= r);

    // Set velocity inside the cylinder to 0 (no-slip condition)
    Zip::from(&mut state.ux)
        .and(&mut state.uy)
        .and(&state.solidity)
        .par_for_each(|ux, uy, &s| {
            if s > 0.0 {
                *ux = 0.0;
                *uy = 0.0;
            }
        });

    // Final check for NaNs in velocities after applying no-slip condition
    // if state.ux.iter().any(|&u| !u.is_finite()) || state.uy.iter().any(|&u| !u.is_finite()) {
    //     panic!("Non-finite velocities detected after applying no-slip condition!");
    // }
}

pub fn apply_boundary_conditions(f: &mut Array3<f64>, solidity: &Array2<f64>, params: &LBMParams) {
    apply_cylinder_boundary(f, solidity, params);
    apply_inlet_boundary(f, params);
    apply_outlet_boundary(f, params);
}

pub fn apply_cylinder_boundary(f: &mut Array3<f64>, solidity: &Array2<f64>, params: &LBMParams) {
    for y in 0..params.ny {
        for x in 0..params.nx {
            if solidity[[y, x]] > 0.0 {
                let mut temp = Array1::<f64>::zeros(params.nl);
                temp.assign(&f.slice(s![y, x, ..]));
                f[[y, x, 1]] = temp[3];
                f[[y, x, 2]] = temp[4];
                f[[y, x, 3]] = temp[1];
                f[[y, x, 4]] = temp[2];
                f[[y, x, 5]] = temp[7];
                f[[y, x, 6]] = temp[8];
                f[[y, x, 7]] = temp[5];
                f[[y, x, 8]] = temp[6];
            }
        }
    }
}

pub fn apply_inlet_boundary(f: &mut Array3<f64>, params: &LBMParams) {
    for y in 0..params.ny {
        // Calculate density at inlet (x=0)
        let rho = f[[y, 0, 0]]
            + f[[y, 0, 2]]
            + f[[y, 0, 4]]
            + 2.0 * (f[[y, 0, 1]] + f[[y, 0, 5]] + f[[y, 0, 8]]);
        // Corrected distribution functions to match MATLAB's inlet boundary condition
        f[[y, 0, 1]] = f[[y, 0, 3]] + (2.0 / 3.0) * rho * params.inlet_velocity;
        // For direction 5
        f[[y, 0, 5]] = f[[y, 0, 7]] - 0.5 * (f[[y, 0, 2]] - f[[y, 0, 4]])
            + (rho * params.inlet_velocity) / 6.0;
        // For direction 8
        f[[y, 0, 8]] = f[[y, 0, 6]]
            + 0.5 * (f[[y, 0, 2]] - f[[y, 0, 4]])
            + (rho * params.inlet_velocity) / 6.0;
    }
}

pub fn apply_outlet_boundary(f: &mut Array3<f64>, params: &LBMParams) {
    for y in 0..params.ny {
        for i in 0..params.nl {
            f[[y, params.nx - 1, i]] = f[[y, params.nx - 2, i]];
        }
    }
}

pub fn perform_collision_step(state: &mut SimulationState, params: &LBMParams) {
    // 1. Compute Equilibrium Distributions in Parallel
    for i in 0..params.nl {
        Zip::from(state.f.slice_mut(s![.., .., i]))
            .and(&state.rho)
            .and(&state.ux)
            .and(&state.uy)
            .par_for_each(|f, &rho_xy, &ux, &uy| {
                let cu = params.cxs[i] as f64 * ux + params.cys[i] as f64 * uy;
                let feq = rho_xy
                    * params.weights[i]
                    * (1.0 + 3.0 * cu + 4.5 * cu * cu - 1.5 * (ux * ux + uy * uy));
                *f += (feq - *f) / params.tau;
                // Safeguards
                if *f < 0.0 {
                    *f = 1e-8;
                }
                if !f.is_finite() {
                    panic!("Non-finite f detected after collision: {}", f);
                }
            });
    }
}
