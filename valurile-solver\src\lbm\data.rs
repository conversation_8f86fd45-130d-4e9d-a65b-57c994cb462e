use ndarray::{Array2, <PERSON>rray3};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct LBMParams {
    pub nx: usize,
    pub ny: usize,
    pub nt: usize,
    pub nl: usize,
    pub rho0: f64,
    pub tau: f64,
    pub inlet_velocity: f64,
    pub cxs: Vec<i32>,
    pub cys: Vec<i32>,
    pub weights: Vec<f64>,
}

impl Default for LBMParams {
    fn default() -> Self {
        Self {
            nx: 200,
            ny: 200,
            nt: 1000,
            nl: 9,
            rho0: 1.0,
            tau: 0.55,
            inlet_velocity: 0.15,
            cxs: vec![0, 1, 0, -1, 0, 1, -1, -1, 1],
            cys: vec![0, 0, 1, 0, -1, 1, 1, -1, -1],
            weights: vec![
                4.0 / 9.0,
                1.0 / 9.0,
                1.0 / 9.0,
                1.0 / 9.0,
                1.0 / 9.0,
                1.0 / 36.0,
                1.0 / 36.0,
                1.0 / 36.0,
                1.0 / 36.0,
            ],
        }
    }
}

pub struct SimulationState {
    pub f: Array3<f64>,
    pub rho: Array2<f64>,
    pub ux: Array2<f64>,
    pub uy: Array2<f64>,
    pub solidity: Array2<f64>,
}
