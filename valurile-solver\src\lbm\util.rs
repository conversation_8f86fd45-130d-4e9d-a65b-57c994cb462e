use ndarray::Array2;

use std::path::Path;

use vtkio::model::*;
use vtkio::IOBuffer;

use colored::*;

use ocl::{
    enums::{DeviceInfo, PlatformInfo},
    Device, Platform,
};

use super::data::*;

pub fn calculate_reynolds_number(params: &LBMParams) -> f64 {
    let viscosity = (params.tau - 0.5) / 3.0;
    let cylinder_diameter = params.ny as f64 / 20.0;
    (params.inlet_velocity * cylinder_diameter / viscosity).round()
}

pub fn export_results(state: &SimulationState, params: &LBMParams, iteration: usize) {
    // println!(
    //     "Iteration {}: mean = {}",
    //     iteration + 1,
    //     state.ux.mean().unwrap_or(0.0)
    // );
    let result = export_lbm_to_vtk(
        params.nx as u32,
        params.ny as u32,
        &state.ux,
        &state.uy,
        "lbm_results.vtk",
        iteration,
    );
    // match result {
    //     Ok(_) => println!(
    //         "Successfully exported VTK file for timestep {}.",
    //         iteration + 1
    //     ),
    //     Err(e) => eprintln!(
    //         "Failed to export VTK file for timestep {}: {}",
    //         iteration + 1,
    //         e
    //     ),
    // }
    match result {
        Ok(_) => (),
        Err(e) => eprintln!(
            "Failed to export VTK file for timestep {}: {}",
            iteration + 1,
            e
        ),
    }
}

pub fn export_lbm_to_vtk(
    nx: u32,
    ny: u32,
    ux: &Array2<f64>,
    uy: &Array2<f64>,
    base_filename: &str,
    timestep: usize,
) -> Result<(), Box<dyn std::error::Error>> {
    // Create coordinate vectors
    let x_coords: Vec<f64> = (0..nx + 1).map(|i| i as f64).collect();
    let y_coords: Vec<f64> = (0..ny + 1).map(|i| i as f64).collect();
    let z_coords = vec![0.0, 1.0];

    // Flatten the 2D arrays into 1D vectors
    let ux_flat: Vec<f64> = ux.iter().cloned().collect();
    let uy_flat: Vec<f64> = uy.iter().cloned().collect();

    // Calculate velocity magnitude
    let velocity_magnitude: Vec<f64> = ux_flat
        .iter()
        .zip(uy_flat.iter())
        .map(|(&ux, &uy)| (ux * ux + uy * uy).sqrt())
        .collect();

    // Create the VTK data set
    let vtk_data = Vtk {
        version: Version::new((0, 1)),
        title: format!("LBM Simulation Results - Timestep {}", timestep),
        byte_order: ByteOrder::BigEndian,
        file_path: None,
        data: DataSet::RectilinearGrid {
            extent: Extent::Dims([nx + 1, ny + 1, 2]),
            meta: None,
            pieces: vec![Piece::Inline(Box::new(RectilinearGridPiece {
                extent: Extent::Dims([nx + 1, ny + 1, 2]),
                coords: Coordinates {
                    x: IOBuffer::F64(x_coords),
                    y: IOBuffer::F64(y_coords),
                    z: IOBuffer::F64(z_coords),
                },
                data: Attributes {
                    cell: vec![
                        Attribute::DataArray(DataArray {
                            name: "velocity_x".to_string(),
                            elem: ElementType::Scalars {
                                num_comp: 1,
                                lookup_table: None,
                            },
                            data: IOBuffer::F64(ux_flat),
                        }),
                        Attribute::DataArray(DataArray {
                            name: "velocity_y".to_string(),
                            elem: ElementType::Scalars {
                                num_comp: 1,
                                lookup_table: None,
                            },
                            data: IOBuffer::F64(uy_flat),
                        }),
                        Attribute::DataArray(DataArray {
                            name: "velocity_magnitude".to_string(),
                            elem: ElementType::Scalars {
                                num_comp: 1,
                                lookup_table: None,
                            },
                            data: IOBuffer::F64(velocity_magnitude),
                        }),
                    ],
                    point: vec![],
                },
            }))],
        },
    };

    // Create the filename with the timestep
    let path = Path::new(base_filename);
    let file_stem = path.file_stem().and_then(|s| s.to_str()).unwrap_or("");
    let extension = path.extension().and_then(|s| s.to_str()).unwrap_or("vtk");
    let filename = format!("results/{}.{:04}.{}", file_stem, timestep, extension);

    // Write the file
    vtk_data.export(&filename)?;
    Ok(())
}

pub fn validate_simulation_state(state: &SimulationState, params: &LBMParams, _iteration: usize) {
    // Check distribution functions
    for y in 0..params.ny {
        for x in 0..params.nx {
            for i in 0..params.nl {
                let f_val = state.f[[y, x, i]];
                if !f_val.is_finite() || f_val < 0.0 {
                    panic!(
                        "Invalid f value detected at (y={}, x={}, i={}) = {}",
                        y, x, i, f_val
                    );
                }
            }
        }
    }

    // Check density
    for y in 0..params.ny {
        for x in 0..params.nx {
            let rho_val = state.rho[[y, x]];
            if !rho_val.is_finite() || rho_val <= 0.0 {
                panic!(
                    "Invalid rho value detected at (y={}, x={}) = {}",
                    y, x, rho_val
                );
            }
        }
    }

    // Check velocities
    for y in 0..params.ny {
        for x in 0..params.nx {
            let ux_val = state.ux[[y, x]];
            let uy_val = state.uy[[y, x]];
            if !ux_val.is_finite() || !uy_val.is_finite() {
                panic!(
                    "Invalid velocity detected at (y={}, x={}) = ({}, {})",
                    y, x, ux_val, uy_val
                );
            }
        }
    }
}

pub fn print_gpu_info() {
    // Information buffer
    let mut info = vec![];
    // Iterate through available OpenCL platforms
    info.push(format!("{}", "\tPlatforms".blue()).to_owned());
    for (platform_index, platform) in Platform::list().into_iter().enumerate() {
        info.push(format!("\t\t{}:", platform_index));
        // Save platform information
        for platform_info_key in &[
            PlatformInfo::Name,
            PlatformInfo::Vendor,
            PlatformInfo::Version,
            PlatformInfo::Profile,
            PlatformInfo::Extensions,
        ] {
            if let Ok(platform_info_value) = platform.info(*platform_info_key) {
                info.push(format!(
                    "\t\t\t{:?}: {}",
                    platform_info_key, platform_info_value
                ));
            }
        }
        // Iterate through available OpenCL devices by platform
        if let Ok(platform_devices) = Device::list_all(platform) {
            info.push(format!("{}", "\tDevices".red().bold().italic()).to_owned());
            for (device_index, device) in platform_devices.into_iter().enumerate() {
                info.push(format!("\t\t\t\t{}:", device_index));
                // Save device information
                for device_info_key in &[
                    DeviceInfo::Type,
                    DeviceInfo::Name,
                    DeviceInfo::Vendor,
                    DeviceInfo::DriverVersion,
                    DeviceInfo::OpenclCVersion,
                    DeviceInfo::Profile,
                    DeviceInfo::MaxComputeUnits,
                    DeviceInfo::MaxWorkGroupSize,
                    DeviceInfo::AddressBits,
                    DeviceInfo::ImageSupport,
                    DeviceInfo::MaxSamplers,
                    DeviceInfo::MemBaseAddrAlign,
                    DeviceInfo::EndianLittle,
                    DeviceInfo::Available,
                    DeviceInfo::BuiltInKernels,
                    DeviceInfo::ParentDevice,
                ] {
                    if let Ok(device_info_value) = device.info(*device_info_key) {
                        info.push(format!(
                            "\t\t\t\t\t{:?}: {}",
                            device_info_key, device_info_value
                        ));
                    }
                }
            }
        }
    }
    // List default platform & devices
    info.push(format!(
        "\tDefault platform: {:?}",
        Platform::list()
            .into_iter()
            .position(|platform| *platform == *Platform::default())
            .expect("Default platform missing?!")
    ));
    info.push(format!("\tDevice specifier: {:?}", Device::specifier()));
    // Print collected information
    println!("OpenCL support:\n{}", info.join("\n"));
}
