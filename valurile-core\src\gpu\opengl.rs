use glow::{HasContext as _, Context};
use glow;

/// OpenGL shader program builder
pub struct ShaderProgramBuilder;

impl ShaderProgramBuilder {
    /// Create a shader program for LBM velocity visualization
    pub fn create_lbm_visualization_program(gl: &Context) -> Result<glow::Program, String> {
        unsafe {
            let program = gl.create_program()
                .map_err(|_| "Failed to create program")?;

            // Vertex shader for fullscreen quad
            let vs_src = r#"#version 330
                layout(location=0) in vec2 a_pos;
                layout(location=1) in vec2 a_uv;
                out vec2 v_uv;
                void main() {
                    v_uv = a_uv;
                    gl_Position = vec4(a_pos, 0.0, 1.0);
                }
            "#;

            // Fragment shader with velocity color mapping
            let fs_src = r#"#version 330
                in vec2 v_uv;
                out vec4 out_color;
                uniform sampler2D u_texture;

                void main() {
                    float val = texture(u_texture, v_uv).r;  // Normalized velocity magnitude
                    
                    // Blue to red gradient
                    vec3 color;
                    
                    // Cool to warm color map
                    if (val < 0.25) {
                        // Dark blue to light blue
                        float t = val / 0.25;
                        color = mix(vec3(0.0, 0.0, 0.5), vec3(0.0, 0.0, 1.0), t);
                    } else if (val < 0.5) {
                        // Light blue to cyan
                        float t = (val - 0.25) / 0.25;
                        color = mix(vec3(0.0, 0.0, 1.0), vec3(0.0, 1.0, 1.0), t);
                    } else if (val < 0.75) {
                        // Cyan to yellow
                        float t = (val - 0.5) / 0.25;
                        color = mix(vec3(0.0, 1.0, 1.0), vec3(1.0, 1.0, 0.0), t);
                    } else {
                        // Yellow to red
                        float t = (val - 0.75) / 0.25;
                        color = mix(vec3(1.0, 1.0, 0.0), vec3(1.0, 0.0, 0.0), t);
                    }
                    
                    // Draw solid objects in white
                    if (val == 0.0) {
                        color = vec3(1.0, 1.0, 1.0);
                    }
                    
                    out_color = vec4(color, 1.0);
                }
            "#;

            let vs = Self::compile_shader(gl, glow::VERTEX_SHADER, vs_src)?;
            let fs = Self::compile_shader(gl, glow::FRAGMENT_SHADER, fs_src)?;

            gl.attach_shader(program, vs);
            gl.attach_shader(program, fs);
            gl.link_program(program);

            if !gl.get_program_link_status(program) {
                let error = gl.get_program_info_log(program);
                gl.delete_program(program);
                return Err(format!("Program link error: {}", error));
            }

            gl.detach_shader(program, vs);
            gl.detach_shader(program, fs);
            gl.delete_shader(vs);
            gl.delete_shader(fs);

            Ok(program)
        }
    }

    unsafe fn compile_shader(gl: &Context, shader_type: u32, source: &str) -> Result<glow::Shader, String> {
        let shader = gl.create_shader(shader_type)
            .map_err(|_| "Failed to create shader")?;
        
        gl.shader_source(shader, source);
        gl.compile_shader(shader);

        if !gl.get_shader_compile_status(shader) {
            let error = gl.get_shader_info_log(shader);
            gl.delete_shader(shader);
            return Err(error);
        }

        Ok(shader)
    }
}

/// OpenGL geometry for fullscreen quad
pub struct FullscreenQuad {
    pub vertex_array: glow::VertexArray,
    pub vertex_buffer: glow::Buffer,
    pub element_buffer: glow::Buffer,
}

impl FullscreenQuad {
    /// Create a fullscreen quad for rendering textures
    pub fn new(gl: &Context) -> Result<Self, String> {
        unsafe {
            let vertex_array = gl.create_vertex_array()
                .map_err(|_| "Failed to create vertex array")?;
            let vertex_buffer = gl.create_buffer()
                .map_err(|_| "Failed to create vertex buffer")?;
            let element_buffer = gl.create_buffer()
                .map_err(|_| "Failed to create element buffer")?;

            gl.bind_vertex_array(Some(vertex_array));

            // Vertex data: position + UV coordinates
            let vertex_data: [f32; 16] = [
                -1.0, -1.0, 0.0, 0.0,  // bottom-left
                 1.0, -1.0, 1.0, 0.0,  // bottom-right
                 1.0,  1.0, 1.0, 1.0,  // top-right
                -1.0,  1.0, 0.0, 1.0,  // top-left
            ];
            let indices: [u32; 6] = [0, 1, 2, 0, 2, 3];

            // Upload vertex data
            gl.bind_buffer(glow::ARRAY_BUFFER, Some(vertex_buffer));
            gl.buffer_data_u8_slice(
                glow::ARRAY_BUFFER,
                bytemuck::cast_slice(&vertex_data),
                glow::STATIC_DRAW,
            );

            // Upload element data
            gl.bind_buffer(glow::ELEMENT_ARRAY_BUFFER, Some(element_buffer));
            gl.buffer_data_u8_slice(
                glow::ELEMENT_ARRAY_BUFFER,
                bytemuck::cast_slice(&indices),
                glow::STATIC_DRAW,
            );

            // Set up vertex attributes
            // Position attribute (location 0)
            gl.enable_vertex_attrib_array(0);
            gl.vertex_attrib_pointer_f32(0, 2, glow::FLOAT, false, 4 * 4, 0);

            // UV attribute (location 1)
            gl.enable_vertex_attrib_array(1);
            gl.vertex_attrib_pointer_f32(1, 2, glow::FLOAT, false, 4 * 4, 2 * 4);

            gl.bind_vertex_array(None);

            Ok(Self {
                vertex_array,
                vertex_buffer,
                element_buffer,
            })
        }
    }

    /// Render the fullscreen quad
    pub fn render(&self, gl: &Context) {
        unsafe {
            gl.bind_vertex_array(Some(self.vertex_array));
            gl.draw_elements(glow::TRIANGLES, 6, glow::UNSIGNED_INT, 0);
        }
    }

    /// Clean up OpenGL resources
    pub fn destroy(&self, gl: &Context) {
        unsafe {
            gl.delete_vertex_array(self.vertex_array);
            gl.delete_buffer(self.vertex_buffer);
            gl.delete_buffer(self.element_buffer);
        }
    }
}

/// Texture utilities for data visualization
pub struct TextureManager;

impl TextureManager {
    /// Create a floating-point texture for velocity data
    pub fn create_velocity_texture(gl: &Context, width: u32, height: u32) -> Result<glow::Texture, String> {
        unsafe {
            let texture = gl.create_texture()
                .map_err(|_| "Failed to create texture")?;
            
            gl.bind_texture(glow::TEXTURE_2D, Some(texture));
            gl.tex_image_2d(
                glow::TEXTURE_2D,
                0,
                glow::R32F as i32,  // Internal format: 1-channel float
                width as i32,
                height as i32,
                0,
                glow::RED,   // Data format
                glow::FLOAT, // Data type
                glow::PixelUnpackData::Slice(None),
            );

            // Set texture parameters
            gl.tex_parameter_i32(
                glow::TEXTURE_2D,
                glow::TEXTURE_MIN_FILTER,
                glow::NEAREST as i32,
            );
            gl.tex_parameter_i32(
                glow::TEXTURE_2D,
                glow::TEXTURE_MAG_FILTER,
                glow::LINEAR as i32,
            );

            gl.bind_texture(glow::TEXTURE_2D, None);

            Ok(texture)
        }
    }

    /// Update texture from PBO data
    pub fn update_from_pbo(gl: &Context, texture: glow::Texture, pbo: glow::Buffer, width: u32, height: u32) {
        unsafe {
            gl.bind_buffer(glow::PIXEL_UNPACK_BUFFER, Some(pbo));
            gl.bind_texture(glow::TEXTURE_2D, Some(texture));
            gl.tex_sub_image_2d(
                glow::TEXTURE_2D,
                0,
                0,
                0,
                width as i32,
                height as i32,
                glow::RED,
                glow::FLOAT,
                glow::PixelUnpackData::BufferOffset(0),
            );
            gl.bind_buffer(glow::PIXEL_UNPACK_BUFFER, None);
        }
    }
}

/// Pixel buffer object for CUDA-OpenGL interop
pub struct PixelBufferManager;

impl PixelBufferManager {
    /// Create a pixel buffer object for data transfer
    pub fn create_pbo(gl: &Context, size_bytes: usize) -> Result<glow::Buffer, String> {
        unsafe {
            let pbo = gl.create_buffer()
                .map_err(|_| "Failed to create PBO")?;
            
            gl.bind_buffer(glow::PIXEL_UNPACK_BUFFER, Some(pbo));
            gl.buffer_data_size(
                glow::PIXEL_UNPACK_BUFFER,
                size_bytes as i32,
                glow::DYNAMIC_DRAW,
            );
            gl.bind_buffer(glow::PIXEL_UNPACK_BUFFER, None);

            Ok(pbo)
        }
    }
} 