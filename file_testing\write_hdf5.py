import h5py
import numpy as np

# Create some example data
nx, ny, nz = 100, 100, 50  # grid dimensions
x = np.linspace(0, 1, nx)
y = np.linspace(0, 1, ny)
z = np.linspace(0, 1, nz)

# Example CFD results (velocities, density)
velocity_x = np.random.rand(nx, ny, nz)
velocity_y = np.random.rand(nx, ny, nz)
velocity_z = np.random.rand(nx, ny, nz)
density = np.random.rand(nx, ny, nz)

# Create an HDF5 file and store the data
with h5py.File('cfd_data.h5', 'w') as f:
    # Store grid coordinates
    f.create_dataset('Grid/X', data=x)
    f.create_dataset('Grid/Y', data=y)
    f.create_dataset('Grid/Z', data=z)
    
    # Store CFD results
    f.create_dataset('Results/VelocityX', data=velocity_x)
    f.create_dataset('Results/VelocityY', data=velocity_y)
    f.create_dataset('Results/VelocityZ', data=velocity_z)
    f.create_dataset('Results/Density', data=density)

