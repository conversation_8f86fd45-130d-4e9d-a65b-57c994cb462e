use crate::tabs::DockTab;
use eframe::egui;
use eframe::egui_glow;
use egui::mutex::Mutex;
use std::sync::atomic::{AtomicBool, AtomicUsize, Ordering};
use std::sync::Arc;
use glow::Context;

// Import from valurile-core
use valurile_core::{LBMSimulator, calculate_reynolds_number};
use valurile_solver::lbm::data::LBMParams;

/// LBM CUDA Visualization Tab - now much simpler!
pub struct LBMCudaTab {
    simulator: Option<Arc<Mutex<LBMSimulator>>>,
    gl: Option<Arc<Context>>,
    running: Arc<AtomicBool>,
    iteration: Arc<AtomicUsize>,
    reynolds: f64,
}

impl LBMCudaTab {
    pub fn new(gl: &Arc<Context>, params: LBMParams) -> Self {
        let reynolds = calculate_reynolds_number(&params);
        
        // Try to create the simulator
        let simulator = match LBMSimulator::new(gl.as_ref(), params) {
            Ok(sim) => Some(Arc::new(Mutex::new(sim))),
            Err(e) => {
                eprintln!("Failed to create LBM simulator: {}", e);
                None
            }
        };

        Self {
            simulator,
            gl: Some(gl.clone()),
            running: Arc::new(AtomicBool::new(false)),
            iteration: Arc::new(AtomicUsize::new(0)),
            reynolds,
        }
    }
}

impl DockTab for LBMCudaTab {
    fn title(&self) -> &str {
        "LBM CUDA Simulation"
    }

    fn ui(&mut self, ui: &mut egui::Ui) {
        if let (Some(gl_arc), Some(simulator_arc)) = (&self.gl, &self.simulator) {
            // Add controls
            ui.horizontal(|ui| {
                let running = self.running.clone();
                let mut is_running = running.load(Ordering::SeqCst);
                if ui.checkbox(&mut is_running, "Run Simulation").changed() {
                    running.store(is_running, Ordering::SeqCst);
                }

                // Display iteration count
                ui.label(format!(
                    "Iteration: {}",
                    self.iteration.load(Ordering::SeqCst)
                ));

                // Display Reynolds number
                ui.label(format!("Reynolds: {:.2}", self.reynolds));
            });

            // Initialize simulation if needed
            let mut simulator = simulator_arc.lock();
            if !simulator.is_initialized() {
                if let Err(e) = simulator.initialize(gl_arc.as_ref()) {
                    ui.label(format!("Initialization failed: {}", e));
                    return;
                }
            }

            // Allocate space for the visualization
            let (rect, _resp) =
                ui.allocate_exact_size(egui::vec2(600.0, 600.0), egui::Sense::hover());

            let simulator_clone = simulator_arc.clone();
            let running_clone = self.running.clone();
            let iteration_clone = self.iteration.clone();

            let cb = egui_glow::CallbackFn::new(move |_info, painter| {
                let mut sim = simulator_clone.lock();

                // Only update if simulation is running
                if running_clone.load(Ordering::SeqCst) {
                    // Run simulation step
                    if let Err(e) = sim.step() {
                        eprintln!("Simulation step failed: {}", e);
                        return;
                    }

                    // Update visualization
                    if let Err(e) = sim.update_visualization(painter.gl()) {
                        eprintln!("Visualization update failed: {}", e);
                        return;
                    }

                    // Increment iteration counter
                    iteration_clone.fetch_add(1, Ordering::SeqCst);
                }

                // Always render the current state
                sim.render(painter.gl());
            });

            ui.painter().add(egui::PaintCallback {
                rect,
                callback: Arc::new(cb),
            });
        } else if self.simulator.is_none() {
            ui.label("Failed to initialize CUDA simulator");
        } else {
            ui.label("No OpenGL context available");
        }
    }

    fn closeable(&mut self) -> bool {
        false
    }
}

impl Drop for LBMCudaTab {
    fn drop(&mut self) {
        if let (Some(gl), Some(simulator)) = (&self.gl, &self.simulator) {
            simulator.lock().destroy(gl.as_ref());
        }
    }
}
