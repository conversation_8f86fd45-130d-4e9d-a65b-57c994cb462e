use crate::tabs::DockTab;
use eframe::egui;

pub struct TableTab {
    pub title: String,
    pub table: Vec<Vec<String>>,
}

impl DockTab for TableTab {
    fn title(&self) -> &str {
        &self.title
    }
    fn ui(&mut self, ui: &mut egui::Ui) {
        ui.heading("Table");
        ui.horizontal(|ui| {
            for row in &self.table {
                ui.vertical(|ui| {
                    for cell in row {
                        ui.label(cell);
                    }
                });
            }
        });
    }
    fn closeable(&mut self) -> bool {
        false
    }
}
