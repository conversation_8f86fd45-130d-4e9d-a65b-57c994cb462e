// compute_macroscopic.cl

#pragma OPENCL EXTENSION cl_khr_fp64 : enable

__kernel void compute_macroscopic(
    __global double* f,
    __global double* rho,
    __global double* ux,
    __global double* uy,
    __global double* solidity,
    __constant int* cxs,
    __constant int* cys,
    int nx,
    int ny,
    int nl
) {
    int y = get_global_id(0);
    int x = get_global_id(1);
    if (y >= ny || x >= nx) return;

    int idx = y * nx + x;
    int f_idx = idx * nl;

    double rho_val = 0.0;
    double ux_val = 0.0;
    double uy_val = 0.0;

    for (int i = 0; i < nl; i++) {
        double f_i = f[f_idx + i];
        rho_val += f_i;
        ux_val += f_i * cxs[i];
        uy_val += f_i * cys[i];
    }

    // Avoid division by zero
    if (rho_val < 1e-8) {
        rho_val = 1e-8;
    }

    ux_val /= rho_val;
    uy_val /= rho_val;

    // No-slip condition inside the cylinder
    if (solidity[idx] > 0.0) {
        ux_val = 0.0;
        uy_val = 0.0;
    }

    rho[idx] = rho_val;
    ux[idx] = ux_val;
    uy[idx] = uy_val;
}

