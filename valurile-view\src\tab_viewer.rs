use egui_dock::TabViewer as EguiDockTabViewer;
use eframe::egui;
use crate::tabs::DockTab;

pub struct TabViewer<T> {
    _phantom: std::marker::PhantomData<T>,
}

impl<T> Default for TabViewer<T> {
    fn default() -> Self {
        Self {
            _phantom: Default::default(),
        }
    }
}

impl<T: DockTab> EguiDockTabViewer for TabViewer<T> {
    type Tab = T;

    fn title(&mut self, tab: &mut Self::Tab) -> egui::WidgetText {
        tab.title().into()
    }

    fn ui(&mut self, ui: &mut egui::Ui, tab: &mut Self::Tab) {
        tab.ui(ui);
    }
    
    fn closeable(&mut self, tab: &mut Self::Tab) -> bool {
        tab.closeable()
    }
}
