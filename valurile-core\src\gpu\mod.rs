pub mod opengl;
pub mod interop;
pub mod vulkan_interop;
pub mod wgpu_backend;
pub mod backend;

pub use opengl::*;
pub use interop::*;
pub use vulkan_interop::*;
pub use wgpu_backend::*;
pub use backend::*;

use glow::{Context, HasContext as _};
use valurile_solver::lbm::data::LBMParams;
use valurile_solver::lbm::cuda_solver::{CudaLBMDevice, LBMCudaState};
pub use cudarc::driver::sys::CUdeviceptr;

/// High-level LBM simulation manager with CUDA compute and OpenGL visualization
pub struct LBMSimulator {
    cuda_device: CudaLBMDevice,
    cuda_state: Option<LBMCudaState>,
    visualization: Option<LBMVisualizationInterop>,
    shader_program: glow::Program,
    fullscreen_quad: FullscreenQuad,
    params: LBMParams,
    current_max_velocity: f32,
    width: u32,
    height: u32,
    time_step: u64,
}

// Safety: OpenGL and CUDA contexts are properly managed
unsafe impl Send for LBMSimulator {}
unsafe impl Sync for LBMSimulator {}

impl LBMSimulator {
    /// Create a new LBM simulator with CUDA and OpenGL (using FluidX3D for maximum performance)
    pub fn new(gl: &Context, params: LBMParams) -> Result<Self, String> {
        Self::new_with_kernel(gl, params, true)
    }

    /// Create a new LBM simulator with option to choose kernel type
    pub fn new_with_kernel(gl: &Context, params: LBMParams, use_fluidx3d: bool) -> Result<Self, String> {
        // Initialize CUDA device with specified kernel type
        let cuda_device = if use_fluidx3d {
            CudaLBMDevice::new_fluidx3d()?
        } else {
            CudaLBMDevice::new()?
        };

        // Create shader program for visualization
        let shader_program = ShaderProgramBuilder::create_lbm_visualization_program(gl)?;

        // Create fullscreen quad for rendering
        let fullscreen_quad = FullscreenQuad::new(gl)?;

        let width = params.nx as u32;
        let height = params.ny as u32;

        Ok(Self {
            cuda_device,
            cuda_state: None,
            visualization: None,
            shader_program,
            fullscreen_quad,
            params,
            current_max_velocity: 0.1,
            width,
            height,
            time_step: 0,
        })
    }

    /// Initialize the simulation (allocate memory and set up visualization)
    pub fn initialize(&mut self, gl: &Context) -> Result<(), String> {
        // Allocate CUDA memory
        let state = self.cuda_device.allocate_lbm_memory(
            self.params.nx,
            self.params.ny,
            self.params.nl,
        )?;

        // Initialize simulation
        self.cuda_device.launch_init(&state, &self.params)?;

        self.cuda_state = Some(state);

        // Set up visualization interop
        let visualization = LBMVisualizationInterop::new(gl, self.width, self.height)?;
        self.visualization = Some(visualization);

        Ok(())
    }

    /// Run one simulation timestep using FluidX3D for maximum performance
    pub fn step(&mut self) -> Result<(), String> {
        if let Some(state) = &self.cuda_state {
            self.time_step += 1;
            self.cuda_device.fluidx3d_simulation_step(state, &self.params, self.time_step)?;
        }
        Ok(())
    }

    /// Compute visualization data and update OpenGL texture
    pub fn update_visualization(&mut self, gl: &Context) -> Result<(), String> {
        if let (Some(state), Some(visualization)) = (&self.cuda_state, &mut self.visualization) {
            visualization.compute_and_update(gl, |output_ptr| {
                self.cuda_device.launch_velocity_magnitude(state, output_ptr, self.current_max_velocity)
            })?;
        }
        Ok(())
    }

    /// Render the current visualization
    pub fn render(&self, gl: &Context) {
        if let Some(visualization) = &self.visualization {
            unsafe {
                gl.use_program(Some(self.shader_program));

                // Bind texture
                visualization.bind_texture(gl, 0);
                if let Some(loc) = gl.get_uniform_location(self.shader_program, "u_texture") {
                    gl.uniform_1_i32(Some(&loc), 0);
                }

                // Render fullscreen quad
                self.fullscreen_quad.render(gl);
            }
        }
    }

    /// Get current simulation parameters
    pub fn params(&self) -> &LBMParams {
        &self.params
    }

    /// Update simulation parameters (requires reinitialization if dimensions change)
    pub fn set_params(&mut self, params: LBMParams) -> bool {
        let needs_reinit = params.nx != self.params.nx || params.ny != self.params.ny;
        self.params = params;
        needs_reinit
    }

    /// Get simulation dimensions
    pub fn dimensions(&self) -> (u32, u32) {
        (self.width, self.height)
    }

    /// Check if simulation is initialized
    pub fn is_initialized(&self) -> bool {
        self.cuda_state.is_some() && self.visualization.is_some()
    }

    /// Clean up resources
    pub fn destroy(&mut self, gl: &Context) {
        if let Some(mut visualization) = self.visualization.take() {
            visualization.destroy(gl);
        }

        unsafe {
            gl.delete_program(self.shader_program);
        }
        self.fullscreen_quad.destroy(gl);

        // CUDA resources are automatically cleaned up
        self.cuda_state = None;
    }
}

/// Helper function to calculate Reynolds number
pub fn calculate_reynolds_number(params: &LBMParams) -> f64 {
    // Re = (characteristic length * velocity) / viscosity
    // For LBM, viscosity = (tau - 0.5) / 3
    let viscosity = (params.tau - 0.5) / 3.0;
    let characteristic_length = params.ny as f64 / 20.0; // Cylinder diameter
    let reynolds = characteristic_length * params.inlet_velocity / viscosity;
    reynolds
}