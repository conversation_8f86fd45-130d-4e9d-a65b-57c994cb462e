use crate::tabs::DockTab;
use egui::Ui;
use std::sync::Arc;
use std::sync::atomic::{AtomicBool, AtomicUsize, Ordering};
use valurile_core::gpu::{RenderingBackend, SimpleLBMWgpuSimulator};
use valurile_solver::lbm::data::LBMParams;
use valurile_solver::lbm::cuda_solver::FLUIDX3D_CUDA_KERNEL_SOURCE;

/// LBM simulation tab using wgpu backend with backend selection
pub struct LBMWgpuTab {
    simulator: Option<SimpleLBMWgpuSimulator>,
    running: Arc<AtomicBool>,
    iteration: Arc<AtomicUsize>,
    backend: RenderingBackend,

    // UI state
    nx: usize,
    ny: usize,
    tau: f64,
    inlet_velocity: f64,
    rho0: f64,

    // Performance metrics
    fps: f32,
    last_frame_time: std::time::Instant,
    frame_count: usize,

    // Backend selection
    available_backends: Vec<RenderingBackend>,
    selected_backend_index: usize,

    // Error state
    last_error: Option<String>,

    // Cached visualization data
    cached_visualization_data: Option<Vec<u8>>,
    last_rendered_time_step: u64,
}

impl Default for LBMWgpuTab {
    fn default() -> Self {
        let available_backends = RenderingBackend::available();
        let preferred = RenderingBackend::preferred();
        let selected_backend_index = available_backends
            .iter()
            .position(|&b| b == preferred)
            .unwrap_or(0);

        Self {
            simulator: None,
            running: Arc::new(AtomicBool::new(false)),
            iteration: Arc::new(AtomicUsize::new(0)),
            backend: preferred,
            nx: 300,  // Better for cylinder visibility
            ny: 150,  // Rectangular domain
            tau: 0.6,
            inlet_velocity: 0.05,  // Higher velocity for visible flow
            rho0: 1.0,
            fps: 0.0,
            last_frame_time: std::time::Instant::now(),
            frame_count: 0,
            available_backends,
            selected_backend_index,
            last_error: None,
            cached_visualization_data: None,
            last_rendered_time_step: 0,
        }
    }
}

impl LBMWgpuTab {
    /// Initialize the simulation with current parameters
    fn initialize_simulation(&mut self) -> Result<(), String> {
        let params = LBMParams {
            nx: self.nx,
            ny: self.ny,
            nt: 1000000, // Large number for continuous simulation
            nl: 9,
            rho0: self.rho0,
            tau: self.tau,
            inlet_velocity: self.inlet_velocity,
            cxs: vec![0, 1, 0, -1, 0, 1, -1, -1, 1],
            cys: vec![0, 0, 1, 0, -1, 1, 1, -1, -1],
            weights: vec![
                4.0 / 9.0,
                1.0 / 9.0,
                1.0 / 9.0,
                1.0 / 9.0,
                1.0 / 9.0,
                1.0 / 36.0,
                1.0 / 36.0,
                1.0 / 36.0,
                1.0 / 36.0,
            ],
        };

        // Create simulator asynchronously
        let mut simulator = pollster::block_on(SimpleLBMWgpuSimulator::new(
            params,
            self.nx as u32,
            self.ny as u32,
            FLUIDX3D_CUDA_KERNEL_SOURCE,
        ))?;

        // Initialize the simulation
        simulator.initialize()?;

        self.simulator = Some(simulator);
        self.iteration.store(0, Ordering::SeqCst);
        self.last_error = None;

        Ok(())
    }

    /// Update performance metrics
    fn update_performance_metrics(&mut self) {
        self.frame_count += 1;
        let now = std::time::Instant::now();
        let elapsed = now.duration_since(self.last_frame_time).as_secs_f32();

        if elapsed >= 1.0 {
            self.fps = self.frame_count as f32 / elapsed;
            self.frame_count = 0;
            self.last_frame_time = now;
        }
    }
}

impl DockTab for LBMWgpuTab {
    fn title(&self) -> &str {
        "LBM wgpu Simulation"
    }

    fn ui(&mut self, ui: &mut Ui) {
        ui.heading("2D LBM Simulation with wgpu");

        // Show error if any
        if let Some(error) = &self.last_error {
            ui.colored_label(egui::Color32::RED, format!("Error: {}", error));
            ui.separator();
        }

        ui.horizontal(|ui| {
            ui.label("Rendering Backend:");
            egui::ComboBox::from_label("")
                .selected_text(self.available_backends[self.selected_backend_index].to_string())
                .show_ui(ui, |ui| {
                    for (i, backend) in self.available_backends.iter().enumerate() {
                        ui.selectable_value(&mut self.selected_backend_index, i, backend.to_string());
                    }
                });

            if ui.button("Apply Backend").clicked() {
                self.backend = self.available_backends[self.selected_backend_index];
                self.simulator = None; // Force reinitialization
            }
        });

        ui.separator();

        // Simulation parameters
        ui.horizontal(|ui| {
            ui.label("Grid Size:");
            ui.add(egui::DragValue::new(&mut self.nx).range(64..=2048).prefix("nx: "));
            ui.add(egui::DragValue::new(&mut self.ny).range(64..=2048).prefix("ny: "));
        });

        ui.horizontal(|ui| {
            ui.label("Physics:");
            ui.add(egui::DragValue::new(&mut self.tau).range(0.5..=2.0).speed(0.01).prefix("τ: "));
            ui.add(egui::DragValue::new(&mut self.inlet_velocity).range(0.001..=0.1).speed(0.001).prefix("v: "));
            ui.add(egui::DragValue::new(&mut self.rho0).range(0.1..=2.0).speed(0.01).prefix("ρ₀: "));
        });

        ui.horizontal(|ui| {
            if ui.button("Initialize").clicked() {
                match self.initialize_simulation() {
                    Ok(()) => {
                        log::info!("LBM wgpu simulation initialized successfully");
                    }
                    Err(e) => {
                        log::error!("Failed to initialize LBM wgpu simulation: {}", e);
                        self.last_error = Some(e);
                    }
                }
            }

            ui.add_enabled_ui(self.simulator.is_some(), |ui| {
                let is_running = self.running.load(Ordering::SeqCst);
                if ui.button(if is_running { "Pause" } else { "Start" }).clicked() {
                    self.running.store(!is_running, Ordering::SeqCst);
                }

                if ui.button("Reset").clicked() {
                    self.running.store(false, Ordering::SeqCst);
                    self.iteration.store(0, Ordering::SeqCst);
                    self.cached_visualization_data = None;
                    self.last_rendered_time_step = 0;
                    if let Some(simulator) = &mut self.simulator {
                        if let Err(e) = simulator.reset() {
                            log::error!("Failed to reset simulation: {}", e);
                            self.last_error = Some(e);
                        }
                    }
                }
            });
        });

        ui.separator();

        // Performance metrics
        ui.horizontal(|ui| {
            ui.label(format!("FPS: {:.1}", self.fps));
            ui.label(format!("Iterations: {}", self.iteration.load(Ordering::SeqCst)));
            ui.label(format!("Backend: {}", self.backend));
            if let Some(simulator) = &self.simulator {
                ui.label(format!("Time Step: {}", simulator.time_step()));
            }
        });

        ui.separator();

        // Visualization area
        if let Some(simulator) = &mut self.simulator {
            // Run simulation step if running
            let is_running = self.running.load(Ordering::SeqCst);
            if is_running {
                let before_time_step = simulator.time_step();
                match simulator.step() {
                    Ok(()) => {
                        // Update visualization
                        if let Err(e) = simulator.update_visualization() {
                            log::error!("Visualization update failed: {}", e);
                            self.last_error = Some(e);
                        } else {
                            let new_iteration = self.iteration.fetch_add(1, Ordering::SeqCst) + 1;
                            let after_time_step = simulator.time_step();


                        }
                    }
                    Err(e) => {
                        log::error!("Simulation step failed: {}", e);
                        self.last_error = Some(e);
                        self.running.store(false, Ordering::SeqCst);
                    }
                }

                // Request repaint to ensure continuous updates
                ui.ctx().request_repaint();
            } else {
                // Debug: Check if we're not running when we should be
                let iteration = self.iteration.load(Ordering::SeqCst);
                if iteration % 120 == 0 && iteration > 0 {
                    println!("DEBUG: Simulation NOT running, iteration={}", iteration);
                }
            }

            // Show visualization using egui image
            ui.allocate_ui_with_layout(
                egui::Vec2::new(ui.available_width(), 400.0),
                egui::Layout::top_down(egui::Align::Center),
                |ui| {
                    egui::Frame::canvas(ui.style()).show(ui, |ui| {
                        if simulator.is_initialized() {
                            // Check if we need to update cached visualization data
                            let current_time_step = simulator.time_step();
                            if self.last_rendered_time_step != current_time_step {
                                // Update cache with new data
                                self.cached_visualization_data = simulator.get_visualization_image_data();
                                self.last_rendered_time_step = current_time_step;

                                // Debug: Track cache updates
                                if current_time_step % 30 == 0 {
                                    println!("DEBUG: Cache updated for time_step={}", current_time_step);
                                }
                            }

                            // Use cached data for rendering
                            if let Some(ref image_data) = self.cached_visualization_data {
                                // Create egui ColorImage from our RGBA data
                                let color_image = egui::ColorImage::from_rgba_unmultiplied(
                                    [self.nx, self.ny],
                                    &image_data,
                                );

                                // Convert to egui texture with unique name per frame
                                let texture_name = format!("lbm_wgpu_visualization_{}", self.iteration.load(Ordering::SeqCst));
                                let texture_handle = ui.ctx().load_texture(
                                    texture_name,
                                    color_image,
                                    egui::TextureOptions::LINEAR,
                                );

                                // Display the image
                                let available_size = ui.available_size();
                                let aspect_ratio = self.nx as f32 / self.ny as f32;
                                let image_size = if available_size.x / aspect_ratio <= available_size.y {
                                    egui::Vec2::new(available_size.x, available_size.x / aspect_ratio)
                                } else {
                                    egui::Vec2::new(available_size.y * aspect_ratio, available_size.y)
                                };

                                ui.centered_and_justified(|ui| {
                                    ui.add(egui::Image::from_texture(&texture_handle).fit_to_exact_size(image_size));
                                });
                            } else {
                                ui.centered_and_justified(|ui| {
                                    ui.label("Loading visualization data...");
                                });
                            }
                        } else {
                            ui.centered_and_justified(|ui| {
                                ui.label("Simulator not initialized");
                            });
                        }
                    });
                },
            );
        } else {
            ui.allocate_ui_with_layout(
                egui::Vec2::new(ui.available_width(), 400.0),
                egui::Layout::top_down(egui::Align::Center),
                |ui| {
                    egui::Frame::canvas(ui.style()).show(ui, |ui| {
                        ui.centered_and_justified(|ui| {
                            ui.label("Click 'Initialize' to start the simulation");
                        });
                    });
                },
            );
        }

        // Update performance metrics
        self.update_performance_metrics();
    }

    fn closeable(&mut self) -> bool {
        true
    }
}
