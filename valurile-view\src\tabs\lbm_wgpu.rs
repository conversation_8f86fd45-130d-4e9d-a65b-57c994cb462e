use crate::tabs::DockTab;
use egui::Ui;
use std::sync::{Arc, Mutex};
use std::sync::atomic::{AtomicBool, AtomicUsize, Ordering};
use valurile_core::gpu::{UnifiedLBMSimulator, RenderingBackend, SimulatorConfig};
use valurile_solver::lbm::data::LBMParams;
use valurile_solver::lbm::cuda_solver::FLUIDX3D_CUDA_KERNEL_SOURCE;

/// LBM simulation tab using wgpu backend
pub struct LBMWgpuTab {
    simulator: Option<Arc<Mutex<UnifiedLBMSimulator>>>,
    running: Arc<AtomicBool>,
    iteration: Arc<AtomicUsize>,
    backend: RenderingBackend,

    // UI state
    nx: usize,
    ny: usize,
    tau: f64,
    inlet_velocity: f64,
    rho0: f64,

    // Performance metrics
    fps: f32,
    last_frame_time: std::time::Instant,
    frame_count: usize,

    // Backend selection
    available_backends: Vec<RenderingBackend>,
    selected_backend_index: usize,
}

impl Default for LBMWgpuTab {
    fn default() -> Self {
        let available_backends = RenderingBackend::available();
        let preferred = RenderingBackend::preferred();
        let selected_backend_index = available_backends
            .iter()
            .position(|&b| b == preferred)
            .unwrap_or(0);

        Self {
            simulator: None,
            running: Arc::new(AtomicBool::new(false)),
            iteration: Arc::new(AtomicUsize::new(0)),
            backend: preferred,
            nx: 512,
            ny: 512,
            tau: 0.6,
            inlet_velocity: 0.01,
            rho0: 1.0,
            fps: 0.0,
            last_frame_time: std::time::Instant::now(),
            frame_count: 0,
            available_backends,
            selected_backend_index,
        }
    }
}

impl LBMWgpuTab {
    /// Initialize the simulation with current parameters
    fn initialize_simulation(&mut self) -> Result<(), String> {
        let params = LBMParams {
            nx: self.nx,
            ny: self.ny,
            nt: 1000000, // Large number for continuous simulation
            nl: 9,
            rho0: self.rho0,
            tau: self.tau,
            inlet_velocity: self.inlet_velocity,
            cxs: vec![0, 1, 0, -1, 0, 1, -1, -1, 1],
            cys: vec![0, 0, 1, 0, -1, 1, 1, -1, -1],
            weights: vec![
                4.0 / 9.0,
                1.0 / 9.0,
                1.0 / 9.0,
                1.0 / 9.0,
                1.0 / 9.0,
                1.0 / 36.0,
                1.0 / 36.0,
                1.0 / 36.0,
                1.0 / 36.0,
            ],
        };

        let config = SimulatorConfig::new(
            self.backend,
            params,
            self.nx as u32,
            self.ny as u32,
            FLUIDX3D_CUDA_KERNEL_SOURCE.to_string(),
        );

        // Create simulator asynchronously
        let simulator = pollster::block_on(config.create_simulator())?;

        self.simulator = Some(Arc::new(Mutex::new(simulator)));
        self.iteration.store(0, Ordering::SeqCst);

        Ok(())
    }

    /// Update performance metrics
    fn update_performance_metrics(&mut self) {
        self.frame_count += 1;
        let now = std::time::Instant::now();
        let elapsed = now.duration_since(self.last_frame_time).as_secs_f32();

        if elapsed >= 1.0 {
            self.fps = self.frame_count as f32 / elapsed;
            self.frame_count = 0;
            self.last_frame_time = now;
        }
    }
}

impl DockTab for LBMWgpuTab {
    fn title(&self) -> &str {
        "LBM wgpu Simulation"
    }

    fn ui(&mut self, ui: &mut Ui) {
        ui.horizontal(|ui| {
            ui.label("Rendering Backend:");
            egui::ComboBox::from_label("")
                .selected_text(self.available_backends[self.selected_backend_index].to_string())
                .show_ui(ui, |ui| {
                    for (i, backend) in self.available_backends.iter().enumerate() {
                        ui.selectable_value(&mut self.selected_backend_index, i, backend.to_string());
                    }
                });

            if ui.button("Apply Backend").clicked() {
                self.backend = self.available_backends[self.selected_backend_index];
                self.simulator = None; // Force reinitialization
            }
        });

        ui.separator();

        // Simulation parameters
        ui.horizontal(|ui| {
            ui.label("Grid Size:");
            ui.add(egui::DragValue::new(&mut self.nx).range(64..=2048).prefix("nx: "));
            ui.add(egui::DragValue::new(&mut self.ny).range(64..=2048).prefix("ny: "));
        });

        ui.horizontal(|ui| {
            ui.label("Physics:");
            ui.add(egui::DragValue::new(&mut self.tau).range(0.5..=2.0).speed(0.01).prefix("τ: "));
            ui.add(egui::DragValue::new(&mut self.inlet_velocity).range(0.001..=0.1).speed(0.001).prefix("v: "));
            ui.add(egui::DragValue::new(&mut self.rho0).range(0.1..=2.0).speed(0.01).prefix("ρ₀: "));
        });

        ui.horizontal(|ui| {
            if ui.button("Initialize").clicked() {
                match self.initialize_simulation() {
                    Ok(()) => {
                        log::info!("LBM wgpu simulation initialized successfully");
                    }
                    Err(e) => {
                        log::error!("Failed to initialize LBM wgpu simulation: {}", e);
                    }
                }
            }

            ui.add_enabled_ui(self.simulator.is_some(), |ui| {
                let is_running = self.running.load(Ordering::SeqCst);
                if ui.button(if is_running { "Pause" } else { "Start" }).clicked() {
                    self.running.store(!is_running, Ordering::SeqCst);
                }

                if ui.button("Reset").clicked() {
                    self.running.store(false, Ordering::SeqCst);
                    self.iteration.store(0, Ordering::SeqCst);
                    if let Err(e) = self.initialize_simulation() {
                        log::error!("Failed to reset simulation: {}", e);
                    }
                }
            });
        });

        ui.separator();

        // Performance metrics
        ui.horizontal(|ui| {
            ui.label(format!("FPS: {:.1}", self.fps));
            ui.label(format!("Iterations: {}", self.iteration.load(Ordering::SeqCst)));
            ui.label(format!("Backend: {}", self.backend));
        });

        ui.separator();

        // Visualization area - simplified for now due to thread safety issues
        ui.allocate_ui_with_layout(
            egui::Vec2::new(ui.available_width(), 400.0),
            egui::Layout::top_down(egui::Align::Center),
            |ui| {
                egui::Frame::canvas(ui.style()).show(ui, |ui| {
                    ui.centered_and_justified(|ui| {
                        if self.simulator.is_some() {
                            ui.label(format!(
                                "wgpu ({}) LBM Simulation\n\nBackend: {}\nIterations: {}\n\n(Full visualization implementation requires\nthread-safe CUDA interop refactoring)",
                                self.backend,
                                self.backend,
                                self.iteration.load(Ordering::SeqCst)
                            ));
                        } else {
                            ui.label("Click 'Initialize' to start the simulation");
                        }
                    });
                });
            },
        );

        // Run simulation in background if running
        if let Some(_simulator_arc) = &self.simulator {
            if self.running.load(Ordering::SeqCst) {
                // For now, just increment the counter to show it's working
                // In a full implementation, we'd run the actual simulation step
                self.iteration.fetch_add(1, Ordering::SeqCst);

                // Limit update rate to avoid overwhelming the UI
                std::thread::sleep(std::time::Duration::from_millis(16)); // ~60 FPS
            }
        }

        // Update performance metrics
        self.update_performance_metrics();
    }

    fn closeable(&mut self) -> bool {
        true
    }
}
