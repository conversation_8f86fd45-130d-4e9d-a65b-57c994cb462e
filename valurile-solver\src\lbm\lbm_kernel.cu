// LBM CUDA Kernels for <PERSON><PERSON><PERSON> Method
// D2Q9 model with separated kernels for initialization, collision, streaming, etc.

// D2Q9 lattice constants
static __constant__ int d_cx[9] = {0, 1, -1, 0, 0, 1, -1, 1, -1};
static __constant__ int d_cy[9] = {0, 0, 0, 1, -1, 1, 1, -1, -1};
static __constant__ float d_w[9] = {4.f / 9.f,  1.f / 9.f,  1.f / 9.f,
                                    1.f / 9.f,  1.f / 9.f,  1.f / 36.f,
                                    1.f / 36.f, 1.f / 36.f, 1.f / 36.f};

// Helper function for atomic max with float (must be defined before use)
__device__ __forceinline__ float atomicMaxFloat(float* address, float val) {
    int* address_as_i = (int*) address;
    int old = *address_as_i, assumed;
    do {
        assumed = old;
        old = atomicCAS(address_as_i, assumed,
            __float_as_int(fmaxf(val, __int_as_float(assumed))));
    } while (assumed != old);
    return __int_as_float(old);
}

// Helper functions
__device__ __forceinline__ int idx2D(int x, int y, int nx, int ny) {
    x = (x + nx) % nx;
    y = (y + ny) % ny;
    return x + y * nx;
}

__device__ bool isInsideCylinder(int x, int y, int nx, int ny) {
    float cx = 0.2f * (float)nx;  // Cylinder center X 
    float cy = 0.5f * (float)ny;  // Cylinder center Y
    float R = 0.05f * (float)ny;  // Cylinder radius
    float dx = (float)x - cx;
    float dy = (float)y - cy;
    return ((dx * dx + dy * dy) <= R * R);
}

__device__ void computeEquilibrium(float rho, float ux, float uy, float *feq) {
    float uu = ux * ux + uy * uy;
    for (int i = 0; i < 9; i++) {
        float cu = d_cx[i] * ux + d_cy[i] * uy;
        feq[i] = d_w[i] * rho * (1.f + 3.f * cu + 4.5f * cu * cu - 1.5f * uu);
    }
}

// Initialize simulation
extern "C" __global__ void lbm_init_kernel(
    float *f, float *rho, float *ux, float *uy, float *solidity,
    int nx, int ny, float rho0, float inlet_velocity
) {
    int x = blockIdx.x * blockDim.x + threadIdx.x;
    int y = blockIdx.y * blockDim.y + threadIdx.y;
    if (x >= nx || y >= ny) return;

    int idx = idx2D(x, y, nx, ny);
    
    // Set solidity (1.0 for solid, 0.0 for fluid)
    solidity[idx] = isInsideCylinder(x, y, nx, ny) ? 1.0f : 0.0f;
    
    // Initialize macroscopic variables
    rho[idx] = rho0;
    ux[idx] = (x == 0) ? inlet_velocity : 0.0f;  // Inlet velocity
    uy[idx] = 0.0f;
    
    // Initialize distribution functions to equilibrium
    float feq[9];
    computeEquilibrium(rho0, ux[idx], uy[idx], feq);
    
    for (int i = 0; i < 9; i++) {
        f[idx + i * nx * ny] = feq[i];
    }
}

// Collision step
extern "C" __global__ void lbm_collision_kernel(
    float *f, float *rho, float *ux, float *uy, float *solidity,
    int nx, int ny, float tau, float rho0
) {
    int x = blockIdx.x * blockDim.x + threadIdx.x;
    int y = blockIdx.y * blockDim.y + threadIdx.y;
    if (x >= nx || y >= ny) return;

    int idx = idx2D(x, y, nx, ny);
    
    // Skip solid cells
    if (solidity[idx] > 0.5f) return;
    
    // Compute macroscopic variables
    float rho_local = 0.0f;
    float ux_local = 0.0f;
    float uy_local = 0.0f;
    
    for (int i = 0; i < 9; i++) {
        float fi = f[idx + i * nx * ny];
        rho_local += fi;
        ux_local += fi * d_cx[i];
        uy_local += fi * d_cy[i];
    }
    
    ux_local /= rho_local;
    uy_local /= rho_local;
    
    // Store macroscopic variables
    rho[idx] = rho_local;
    ux[idx] = ux_local;
    uy[idx] = uy_local;
    
    // Collision
    float feq[9];
    computeEquilibrium(rho_local, ux_local, uy_local, feq);
    
    float omega = 1.0f / tau;
    for (int i = 0; i < 9; i++) {
        float fi = f[idx + i * nx * ny];
        f[idx + i * nx * ny] = fi + omega * (feq[i] - fi);
    }
}

// Streaming step
extern "C" __global__ void lbm_streaming_kernel(
    float *f, float *f_new, int nx, int ny
) {
    int x = blockIdx.x * blockDim.x + threadIdx.x;
    int y = blockIdx.y * blockDim.y + threadIdx.y;
    if (x >= nx || y >= ny) return;

    int idx = idx2D(x, y, nx, ny);
    
    for (int i = 0; i < 9; i++) {
        // Stream from neighbor
        int xn = x - d_cx[i];
        int yn = y - d_cy[i];
        int idx_neighbor = idx2D(xn, yn, nx, ny);
        
        f_new[idx + i * nx * ny] = f[idx_neighbor + i * nx * ny];
    }
}

// Boundary conditions
extern "C" __global__ void lbm_boundary_kernel(
    float *f, float *solidity, int nx, int ny, float inlet_velocity
) {
    int x = blockIdx.x * blockDim.x + threadIdx.x;
    int y = blockIdx.y * blockDim.y + threadIdx.y;
    if (x >= nx || y >= ny) return;

    int idx = idx2D(x, y, nx, ny);
    
    // Cylinder boundary (bounce-back)
    if (solidity[idx] > 0.5f) {
        // Bounce-back: swap opposite directions
        float temp;
        temp = f[idx + 1 * nx * ny]; f[idx + 1 * nx * ny] = f[idx + 2 * nx * ny]; f[idx + 2 * nx * ny] = temp;
        temp = f[idx + 3 * nx * ny]; f[idx + 3 * nx * ny] = f[idx + 4 * nx * ny]; f[idx + 4 * nx * ny] = temp;
        temp = f[idx + 5 * nx * ny]; f[idx + 5 * nx * ny] = f[idx + 7 * nx * ny]; f[idx + 7 * nx * ny] = temp;
        temp = f[idx + 6 * nx * ny]; f[idx + 6 * nx * ny] = f[idx + 8 * nx * ny]; f[idx + 8 * nx * ny] = temp;
    }
    
    // Inlet boundary (left wall)
    if (x == 0) {
        float rho_wall = 1.0f;
        float ux_wall = inlet_velocity;
        float uy_wall = 0.0f;
        
        float feq[9];
        computeEquilibrium(rho_wall, ux_wall, uy_wall, feq);
        
        for (int i = 0; i < 9; i++) {
            f[idx + i * nx * ny] = feq[i];
        }
    }
    
    // Outlet boundary (right wall - zero gradient)
    if (x == nx - 1) {
        int idx_prev = idx2D(x - 1, y, nx, ny);
        for (int i = 0; i < 9; i++) {
            f[idx + i * nx * ny] = f[idx_prev + i * nx * ny];
        }
    }
}

// Compute macroscopic variables
extern "C" __global__ void lbm_macroscopic_kernel(
    float *f, float *rho, float *ux, float *uy, float *solidity,
    int nx, int ny
) {
    int x = blockIdx.x * blockDim.x + threadIdx.x;
    int y = blockIdx.y * blockDim.y + threadIdx.y;
    if (x >= nx || y >= ny) return;

    int idx = idx2D(x, y, nx, ny);
    
    // Skip solid cells
    if (solidity[idx] > 0.5f) {
        rho[idx] = 1.0f;
        ux[idx] = 0.0f;
        uy[idx] = 0.0f;
        return;
    }
    
    float rho_local = 0.0f;
    float ux_local = 0.0f;
    float uy_local = 0.0f;
    
    for (int i = 0; i < 9; i++) {
        float fi = f[idx + i * nx * ny];
        rho_local += fi;
        ux_local += fi * d_cx[i];
        uy_local += fi * d_cy[i];
    }
    
    rho[idx] = rho_local;
    ux[idx] = ux_local / rho_local;
    uy[idx] = uy_local / rho_local;
}

// Find maximum velocity
extern "C" __global__ void lbm_max_velocity_kernel(
    float *ux, float *uy, float *max_vel, int nx, int ny
) {
    int x = blockIdx.x * blockDim.x + threadIdx.x;
    int y = blockIdx.y * blockDim.y + threadIdx.y;
    if (x >= nx || y >= ny) return;

    int idx = idx2D(x, y, nx, ny);
    float vel_mag = sqrtf(ux[idx] * ux[idx] + uy[idx] * uy[idx]);
    
    atomicMaxFloat(max_vel, vel_mag);
}

// Compute velocity magnitude for visualization
extern "C" __global__ void lbm_velocity_magnitude_kernel(
    float *ux, float *uy, float *output, int nx, int ny, float max_velocity
) {
    int x = blockIdx.x * blockDim.x + threadIdx.x;
    int y = blockIdx.y * blockDim.y + threadIdx.y;
    if (x >= nx || y >= ny) return;

    int idx = idx2D(x, y, nx, ny);
    float vel_mag = sqrtf(ux[idx] * ux[idx] + uy[idx] * uy[idx]);
    
    // Normalize by max velocity for visualization
    output[idx] = (max_velocity > 0.0f) ? (vel_mag / max_velocity) : 0.0f;
} 