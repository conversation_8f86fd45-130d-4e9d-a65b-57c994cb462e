import h5py
import os
from paraview.simple import *
import vtk

h5_file_path = "C://Users//garre//dev//valurile//file_testing//cfd_data.h5"

# Open the HDF5 file
with h5py.File(h5_file_path, 'r') as f:
    # Load grid data
    x = f['Grid/X'][:]
    y = f['Grid/Y'][:]
    z = f['Grid/Z'][:]
    
    # Load field data
    velocity_x = f['Results/VelocityX'][:]
    velocity_y = f['Results/VelocityY'][:]
    velocity_z = f['Results/VelocityZ'][:]
    density = f['Results/Density'][:]

# Create vtkRectilinearGrid object
rectilinear_grid = vtk.vtkRectilinearGrid()
rectilinear_grid.SetDimensions(len(x), len(y), len(z))

# Convert NumPy arrays to vtk arrays
def numpy_to_vtk_array(np_array):
    vtk_data_array = vtk.vtkFloatArray()
    vtk_data_array.SetNumberOfValues(np_array.size)
    vtk_data_array.SetVoidArray(np_array, np_array.size, 1)
    return vtk_data_array

# Set grid coordinates
rectilinear_grid.SetXCoordinates(numpy_to_vtk_array(x))
rectilinear_grid.SetYCoordinates(numpy_to_vtk_array(y))
rectilinear_grid.SetZCoordinates(numpy_to_vtk_array(z))

# Add the velocity fields as point data to the grid
velocity_x_vtk = numpy_to_vtk_array(velocity_x.ravel())
velocity_x_vtk.SetName("VelocityX")
rectilinear_grid.GetPointData().AddArray(velocity_x_vtk)

velocity_y_vtk = numpy_to_vtk_array(velocity_y.ravel())
velocity_y_vtk.SetName("VelocityY")
rectilinear_grid.GetPointData().AddArray(velocity_y_vtk)

velocity_z_vtk = numpy_to_vtk_array(velocity_z.ravel())
velocity_z_vtk.SetName("VelocityZ")
rectilinear_grid.GetPointData().AddArray(velocity_z_vtk)

# Add density field
density_vtk = numpy_to_vtk_array(density.ravel())
density_vtk.SetName("Density")
rectilinear_grid.GetPointData().AddArray(density_vtk)

# Use TrivialProducer to wrap the vtkRectilinearGrid so ParaView can render it
producer = TrivialProducer()
producer.GetClientSideObject().SetOutput(rectilinear_grid)

# Show the grid in ParaView
Show(producer)
Render()