use egui::{Color32, Shadow, Stroke, Style, Visuals};

/// Custom dark blue theme for the application inspired by modern themes like Catppuccin and re_ui
pub fn apply_dark_blue_theme(ctx: &egui::Context) {
    let mut style = Style::default();
    
    // Set the overall theme to dark
    style.visuals = Visuals::dark();
    
    // Define our custom dark blue color palette - inspired by deep ocean/midnight themes
    let base = Color32::from_rgb(16, 20, 32);           // Deep dark blue base
    let surface0 = Color32::from_rgb(24, 30, 46);       // Surface level 0
    let surface1 = Color32::from_rgb(32, 40, 60);       // Surface level 1  
    let surface2 = Color32::from_rgb(40, 50, 74);       // Surface level 2
    let overlay = Color32::from_rgb(48, 60, 88);        // Overlay color
    
    let text = Color32::from_rgb(205, 214, 244);        // Main text color
    let _subtext1 = Color32::from_rgb(186, 194, 222);    // Secondary text
    let subtext0 = Color32::from_rgb(166, 173, 200);    // Tertiary text
    
    let blue = Color32::from_rgb(137, 180, 250);        // Bright blue accent
    let sapphire = Color32::from_rgb(116, 199, 236);    // Sapphire blue
    let sky = Color32::from_rgb(137, 220, 235);         // Sky blue
    let _teal = Color32::from_rgb(148, 226, 213);        // Teal accent
    let _green = Color32::from_rgb(166, 227, 161);       // Green accent
    let yellow = Color32::from_rgb(249, 226, 175);      // Yellow accent
    let _peach = Color32::from_rgb(250, 179, 135);       // Peach accent
    let red = Color32::from_rgb(243, 139, 168);         // Red accent
    
    // Background colors
    style.visuals.window_fill = base;
    style.visuals.panel_fill = surface0;
    style.visuals.faint_bg_color = surface1;
    style.visuals.extreme_bg_color = base;
    
    // Widget colors - noninteractive (disabled/readonly)
    style.visuals.widgets.noninteractive.bg_fill = surface1;
    style.visuals.widgets.noninteractive.weak_bg_fill = surface0;
    style.visuals.widgets.noninteractive.bg_stroke = Stroke::new(1.0, overlay);
    style.visuals.widgets.noninteractive.fg_stroke = Stroke::new(1.0, subtext0);
    
    // Widget colors - inactive (normal state)
    style.visuals.widgets.inactive.bg_fill = surface1;
    style.visuals.widgets.inactive.weak_bg_fill = surface0;
    style.visuals.widgets.inactive.bg_stroke = Stroke::new(1.0, overlay);
    style.visuals.widgets.inactive.fg_stroke = Stroke::new(1.0, text);
    
    // Widget colors - hovered
    style.visuals.widgets.hovered.bg_fill = surface2;
    style.visuals.widgets.hovered.weak_bg_fill = overlay;
    style.visuals.widgets.hovered.bg_stroke = Stroke::new(1.5, blue);
    style.visuals.widgets.hovered.fg_stroke = Stroke::new(1.5, text);
    
    // Widget colors - active (pressed/selected)
    style.visuals.widgets.active.bg_fill = blue;
    style.visuals.widgets.active.weak_bg_fill = sapphire;
    style.visuals.widgets.active.bg_stroke = Stroke::new(2.0, sky);
    style.visuals.widgets.active.fg_stroke = Stroke::new(2.0, base);
    
    // Widget colors - open (dropdowns, etc.)
    style.visuals.widgets.open.bg_fill = surface2;
    style.visuals.widgets.open.weak_bg_fill = overlay;
    style.visuals.widgets.open.bg_stroke = Stroke::new(1.5, blue);
    style.visuals.widgets.open.fg_stroke = Stroke::new(1.5, text);
    
    // Selection colors
    style.visuals.selection.bg_fill = Color32::from_rgba_unmultiplied(137, 180, 250, 60);
    style.visuals.selection.stroke = Stroke::new(1.0, blue);
    
    // Text colors
    style.visuals.override_text_color = Some(text);
    
    // Window and popup styling with modern rounded corners
    style.visuals.window_shadow = Shadow {
        offset: [6, 8].into(),
        blur: 16,
        spread: 0,
        color: Color32::from_black_alpha(120),
    };
    style.visuals.popup_shadow = Shadow {
        offset: [4, 6].into(),
        blur: 12,
        spread: 0,
        color: Color32::from_black_alpha(100),
    };
    
    // Button styling
    style.visuals.button_frame = true;
    
    // Border and stroke colors
    style.visuals.window_stroke = Stroke::new(1.0, overlay);
    style.visuals.resize_corner_size = 12.0;
    
    // Hyperlink colors
    style.visuals.hyperlink_color = sapphire;
    
    // Status colors
    style.visuals.error_fg_color = red;
    style.visuals.warn_fg_color = yellow;
    
    // Code/monospace colors
    style.visuals.code_bg_color = surface1;
    
    // Spacing and sizing for a clean, modern look
    style.spacing.item_spacing = egui::Vec2::new(4.0, 4.0);
    style.spacing.button_padding = egui::Vec2::new(8.0, 4.0);
    style.spacing.menu_margin = egui::Margin::same(8);
    style.spacing.indent = 24.0;
    style.spacing.window_margin = egui::Margin::same(8);
    style.spacing.combo_width = 100.0;
    style.spacing.text_edit_width = 280.0;
    style.spacing.icon_width = 16.0;
    style.spacing.icon_spacing = 2.0;
    style.spacing.tooltip_width = 600.0;
    
    // Interaction settings for better UX
    style.interaction.resize_grab_radius_side = 8.0;
    style.interaction.resize_grab_radius_corner = 12.0;
    style.interaction.show_tooltips_only_when_still = true;
    
    // Apply the custom style
    ctx.set_style(style);
}

/// Alternative lighter blue theme for contrast
pub fn apply_light_blue_theme(ctx: &egui::Context) {
    let mut style = Style::default();
    
    // Set the overall theme to light
    style.visuals = Visuals::light();
    
    // Define light blue color palette
    let base = Color32::from_rgb(239, 241, 245);        // Light base
    let surface0 = Color32::from_rgb(230, 233, 239);    // Surface level 0
    let surface1 = Color32::from_rgb(220, 224, 232);    // Surface level 1
    let surface2 = Color32::from_rgb(210, 215, 225);    // Surface level 2
    let overlay = Color32::from_rgb(200, 206, 218);     // Overlay color
    
    let text = Color32::from_rgb(76, 79, 105);          // Main text color
    let _subtext1 = Color32::from_rgb(92, 95, 119);      // Secondary text
    let _subtext0 = Color32::from_rgb(108, 111, 133);    // Tertiary text
    
    let blue = Color32::from_rgb(30, 102, 245);         // Bright blue accent
    let sapphire = Color32::from_rgb(32, 159, 181);     // Sapphire blue
    let _sky = Color32::from_rgb(4, 165, 229);           // Sky blue
    
    // Apply similar structure as dark theme but with light colors
    style.visuals.window_fill = base;
    style.visuals.panel_fill = surface0;
    style.visuals.faint_bg_color = surface1;
    style.visuals.extreme_bg_color = Color32::WHITE;
    
    // Widget styling for light theme
    style.visuals.widgets.inactive.bg_fill = surface1;
    style.visuals.widgets.inactive.bg_stroke = Stroke::new(1.0, overlay);
    style.visuals.widgets.inactive.fg_stroke = Stroke::new(1.0, text);
    
    style.visuals.widgets.hovered.bg_fill = surface2;
    style.visuals.widgets.hovered.bg_stroke = Stroke::new(1.5, blue);
    style.visuals.widgets.hovered.fg_stroke = Stroke::new(1.5, text);
    
    style.visuals.widgets.active.bg_fill = blue;
    style.visuals.widgets.active.bg_stroke = Stroke::new(2.0, sapphire);
    style.visuals.widgets.active.fg_stroke = Stroke::new(2.0, Color32::WHITE);
    
    // Text colors
    style.visuals.override_text_color = Some(text);
    
    // Apply spacing
    style.spacing.item_spacing = egui::Vec2::new(8.0, 8.0);
    style.spacing.button_padding = egui::Vec2::new(16.0, 8.0);
    style.spacing.window_margin = egui::Margin::same(12);
    
    ctx.set_style(style);
} 