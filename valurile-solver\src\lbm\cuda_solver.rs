// ### Cudarc imports ###
use cudarc::driver::{<PERSON>uda<PERSON>ontext, <PERSON>udaFunction, <PERSON>udaSlice, CudaStream, CudaModule, LaunchConfig, DevicePtr, PushKernelArg};
use cudarc::nvrtc::{compile_ptx_with_opts, CompileOptions};

// ### Raw driver FFI for interop ###
use cudarc::driver::sys::CUgraphicsRegisterFlags_enum::CU_GRAPHICS_REGISTER_FLAGS_NONE;
use cudarc::driver::sys::CUresult::CUDA_SUCCESS;
use cudarc::driver::sys::{CUdeviceptr, CUgraphicsResource, CUresult};

use std::sync::Arc;

// Bring in LBM parameters
use super::data::LBMParams;

// This is our manual extern for `cuGraphicsGLRegisterBuffer`
pub mod cuda_gl_interop {
    use std::os::raw::c_uint;
    #[link(name = "cuda")]
    extern "C" {
        pub fn cuGraphicsGLRegisterBuffer(
            pResource: *mut super::CUgraphicsResource,
            buffer: c_uint,
            flags: c_uint,
        ) -> super::CUresult;
    }
}

/// Embedded FluidX3D CUDA kernel source for LBM simulation
pub static FLUIDX3D_CUDA_KERNEL_SOURCE: &str = include_str!("esoteric_pull_kernel.cu");

/// Legacy LBM kernel source (for compatibility)
pub static LBM_CUDA_KERNEL_SOURCE: &str = include_str!("lbm_kernel.cu");

/// Wrapper for CUDA device memory buffers
pub struct CudaBuffers {
    pub f: CudaSlice<f32>,
    pub f_new: CudaSlice<f32>,
    pub rho: CudaSlice<f32>,
    pub ux: CudaSlice<f32>,
    pub uy: CudaSlice<f32>,
    pub solidity: CudaSlice<f32>,
    pub max_velocity: CudaSlice<f32>,
}

/// Raw CUDA device pointers for kernel launching
pub struct CudaDevicePointers {
    pub f: CUdeviceptr,
    pub f_new: CUdeviceptr,
    pub rho: CUdeviceptr,
    pub ux: CUdeviceptr,
    pub uy: CUdeviceptr,
    pub solidity: CUdeviceptr,
    pub max_velocity: CUdeviceptr,
}

/// LBM simulation state in CUDA memory
pub struct LBMCudaState {
    pub ptrs: CudaDevicePointers,
    pub buffers: CudaBuffers,
    pub nx: usize,
    pub ny: usize,
    pub nl: usize,
    pub size_bytes_3d: usize,
    pub size_bytes_2d: usize,
    pub size_bytes_scalar: usize,
}

/// CUDA kernel functions for LBM simulation
pub struct LBMKernels {
    pub init: CudaFunction,
    pub collision: CudaFunction,
    pub streaming: CudaFunction,
    pub boundary: CudaFunction,
    pub macroscopic: CudaFunction,
    pub max_velocity: CudaFunction,
    pub velocity_magnitude: CudaFunction,
    // FluidX3D kernels
    pub stream_collide: Option<CudaFunction>,
    pub update_timestep: Option<CudaFunction>,
    pub performance_monitor: Option<CudaFunction>,
}

/// CUDA device manager for LBM simulations
pub struct CudaLBMDevice {
    pub context: Arc<CudaContext>,
    pub stream: Arc<CudaStream>,
    pub module: Arc<CudaModule>,
    pub kernels: LBMKernels,
}

impl CudaLBMDevice {
    /// Create a new CUDA device and compile LBM kernels
    pub fn new() -> Result<Self, String> {
        Self::new_with_kernel_source(LBM_CUDA_KERNEL_SOURCE)
    }

    /// Create a new CUDA device with FluidX3D kernels for maximum performance
    pub fn new_fluidx3d() -> Result<Self, String> {
        Self::new_with_kernel_source(FLUIDX3D_CUDA_KERNEL_SOURCE)
    }

    /// Create a new CUDA device with custom kernel source
    pub fn new_with_kernel_source(kernel_source: &str) -> Result<Self, String> {
        let context = CudaContext::new(0).map_err(|e| format!("Failed to create CUDA context: {}", e))?;
        let stream = context.default_stream();

        let compile_opts = CompileOptions {
            ..Default::default()
        };

        let ptx_data = compile_ptx_with_opts(kernel_source, compile_opts)
            .map_err(|e| format!("Failed to compile LBM kernel: {}", e))?;

        // Load PTX into context as module
        let module = context.load_module(ptx_data)
            .map_err(|e| format!("Failed to load PTX: {}", e))?;

        // Get kernel functions
        let kernels = LBMKernels {
            init: module.load_function("lbm_init_kernel")
                .map_err(|e| format!("Failed to get init kernel: {}", e))?,
            collision: module.load_function("lbm_collision_kernel")
                .map_err(|e| format!("Failed to get collision kernel: {}", e))?,
            streaming: module.load_function("lbm_streaming_kernel")
                .map_err(|e| format!("Failed to get streaming kernel: {}", e))?,
            boundary: module.load_function("lbm_boundary_kernel")
                .map_err(|e| format!("Failed to get boundary kernel: {}", e))?,
            macroscopic: module.load_function("lbm_macroscopic_kernel")
                .map_err(|e| format!("Failed to get macroscopic kernel: {}", e))?,
            max_velocity: module.load_function("lbm_max_velocity_kernel")
                .map_err(|e| format!("Failed to get max_velocity kernel: {}", e))?,
            velocity_magnitude: module.load_function("lbm_velocity_magnitude_kernel")
                .map_err(|e| format!("Failed to get velocity_magnitude kernel: {}", e))?,
            // FluidX3D kernels (optional)
            stream_collide: module.load_function("lbm_stream_collide_kernel").ok(),
            update_timestep: module.load_function("lbm_update_timestep_kernel").ok(),
            performance_monitor: module.load_function("lbm_performance_monitor_kernel").ok(),
        };

        context.bind_to_thread().map_err(|e| format!("Failed to bind context: {}", e))?;

        Ok(Self {
            context,
            stream,
            module,
            kernels,
        })
    }

    /// Allocate CUDA memory for LBM simulation
    pub fn allocate_lbm_memory(&self, nx: usize, ny: usize, nl: usize) -> Result<LBMCudaState, String> {
        unsafe {
            // Allocate buffers using the stream
            let f = self.stream.alloc::<f32>(nx * ny * nl)
                .map_err(|e| format!("Failed to allocate f buffer: {}", e))?;
            let f_new = self.stream.alloc::<f32>(nx * ny * nl)
                .map_err(|e| format!("Failed to allocate f_new buffer: {}", e))?;
            let rho = self.stream.alloc::<f32>(nx * ny)
                .map_err(|e| format!("Failed to allocate rho buffer: {}", e))?;
            let ux = self.stream.alloc::<f32>(nx * ny)
                .map_err(|e| format!("Failed to allocate ux buffer: {}", e))?;
            let uy = self.stream.alloc::<f32>(nx * ny)
                .map_err(|e| format!("Failed to allocate uy buffer: {}", e))?;
            let solidity = self.stream.alloc::<f32>(nx * ny)
                .map_err(|e| format!("Failed to allocate solidity buffer: {}", e))?;
            let max_velocity = self.stream.alloc::<f32>(1)
                .map_err(|e| format!("Failed to allocate max_velocity buffer: {}", e))?;

            // Extract device pointers
            let ptrs = CudaDevicePointers {
                f: f.device_ptr(&self.stream).0 as CUdeviceptr,
                f_new: f_new.device_ptr(&self.stream).0 as CUdeviceptr,
                rho: rho.device_ptr(&self.stream).0 as CUdeviceptr,
                ux: ux.device_ptr(&self.stream).0 as CUdeviceptr,
                uy: uy.device_ptr(&self.stream).0 as CUdeviceptr,
                solidity: solidity.device_ptr(&self.stream).0 as CUdeviceptr,
                max_velocity: max_velocity.device_ptr(&self.stream).0 as CUdeviceptr,
            };

            let buffers = CudaBuffers {
                f,
                f_new,
                rho,
                ux,
                uy,
                solidity,
                max_velocity,
            };

            Ok(LBMCudaState {
                ptrs,
                buffers,
                nx,
                ny,
                nl,
                size_bytes_3d: nx * ny * nl * std::mem::size_of::<f32>(),
                size_bytes_2d: nx * ny * std::mem::size_of::<f32>(),
                size_bytes_scalar: std::mem::size_of::<f32>(),
            })
        }
    }

    /// Launch initialization kernel
    pub fn launch_init(&self, state: &LBMCudaState, params: &LBMParams) -> Result<(), String> {
        let launch_cfg = Self::get_2d_launch_config(state.nx, state.ny);

        unsafe {
            self.stream.launch_builder(&self.kernels.init)
                .arg(&state.ptrs.f)
                .arg(&state.ptrs.rho)
                .arg(&state.ptrs.ux)
                .arg(&state.ptrs.uy)
                .arg(&state.ptrs.solidity)
                .arg(&(state.nx as i32))
                .arg(&(state.ny as i32))
                .arg(&(params.rho0 as f32))
                .arg(&(params.inlet_velocity as f32))
                .launch(launch_cfg)
                .map_err(|e| format!("Init kernel error: {}", e))?;
            Ok(())
        }
    }

    /// Launch collision kernel
    pub fn launch_collision(&self, state: &LBMCudaState, params: &LBMParams) -> Result<(), String> {
        let launch_cfg = Self::get_2d_launch_config(state.nx, state.ny);

        unsafe {
            self.stream.launch_builder(&self.kernels.collision)
                .arg(&state.ptrs.f)
                .arg(&state.ptrs.rho)
                .arg(&state.ptrs.ux)
                .arg(&state.ptrs.uy)
                .arg(&state.ptrs.solidity)
                .arg(&(state.nx as i32))
                .arg(&(state.ny as i32))
                .arg(&(params.tau as f32))
                .arg(&(params.rho0 as f32))
                .launch(launch_cfg)
                .map_err(|e| format!("Collision kernel error: {}", e))?;
            Ok(())
        }
    }

    /// Launch streaming kernel
    pub fn launch_streaming(&self, state: &LBMCudaState) -> Result<(), String> {
        let launch_cfg = Self::get_2d_launch_config(state.nx, state.ny);

        unsafe {
            self.stream.launch_builder(&self.kernels.streaming)
                .arg(&state.ptrs.f)
                .arg(&state.ptrs.f_new)
                .arg(&(state.nx as i32))
                .arg(&(state.ny as i32))
                .launch(launch_cfg)
                .map_err(|e| format!("Streaming kernel error: {}", e))?;

            // Copy f_new back to f using stream memcpy
            self.stream.memcpy_dtod(&state.buffers.f_new, &mut state.buffers.f.clone())
                .map_err(|e| format!("Failed to copy f_new to f: {}", e))?;
        }

        Ok(())
    }

    /// Launch boundary kernel
    pub fn launch_boundary(&self, state: &LBMCudaState, params: &LBMParams) -> Result<(), String> {
        let launch_cfg = Self::get_2d_launch_config(state.nx, state.ny);

        unsafe {
            self.stream.launch_builder(&self.kernels.boundary)
                .arg(&state.ptrs.f)
                .arg(&state.ptrs.solidity)
                .arg(&(state.nx as i32))
                .arg(&(state.ny as i32))
                .arg(&(params.inlet_velocity as f32))
                .launch(launch_cfg)
                .map_err(|e| format!("Boundary kernel error: {}", e))?;
            Ok(())
        }
    }

    /// Launch macroscopic kernel
    pub fn launch_macroscopic(&self, state: &LBMCudaState) -> Result<(), String> {
        let launch_cfg = Self::get_2d_launch_config(state.nx, state.ny);

        unsafe {
            self.stream.launch_builder(&self.kernels.macroscopic)
                .arg(&state.ptrs.f)
                .arg(&state.ptrs.rho)
                .arg(&state.ptrs.ux)
                .arg(&state.ptrs.uy)
                .arg(&state.ptrs.solidity)
                .arg(&(state.nx as i32))
                .arg(&(state.ny as i32))
                .launch(launch_cfg)
                .map_err(|e| format!("Macroscopic kernel error: {}", e))?;
            Ok(())
        }
    }

    /// Launch velocity magnitude kernel (writes to external buffer)
    pub fn launch_velocity_magnitude(&self, state: &LBMCudaState, output_ptr: CUdeviceptr, max_velocity: f32) -> Result<(), String> {
        let launch_cfg = Self::get_2d_launch_config(state.nx, state.ny);

        unsafe {
            self.stream.launch_builder(&self.kernels.velocity_magnitude)
                .arg(&state.ptrs.ux)
                .arg(&state.ptrs.uy)
                .arg(&output_ptr)
                .arg(&(state.nx as i32))
                .arg(&(state.ny as i32))
                .arg(&max_velocity)
                .launch(launch_cfg)
                .map_err(|e| format!("Velocity magnitude kernel error: {}", e))?;
            Ok(())
        }
    }

    /// Copy velocity data from GPU to host memory for visualization
    pub fn copy_velocity_to_host(&self, state: &LBMCudaState) -> Result<(Vec<f32>, Vec<f32>), String> {
        let mut ux_host = vec![0.0f32; state.nx * state.ny];
        let mut uy_host = vec![0.0f32; state.nx * state.ny];

        // Use raw CUDA driver API for memory copy
        unsafe {
            use cudarc::driver::sys::*;

            let size_bytes = (state.nx * state.ny * std::mem::size_of::<f32>()) as usize;

            // Copy ux data
            let status = cuMemcpyDtoH_v2(
                ux_host.as_mut_ptr() as *mut std::ffi::c_void,
                state.ptrs.ux,
                size_bytes,
            );
            if status != CUDA_SUCCESS {
                return Err(format!("Failed to copy ux to host: {:?}", status));
            }

            // Copy uy data
            let status = cuMemcpyDtoH_v2(
                uy_host.as_mut_ptr() as *mut std::ffi::c_void,
                state.ptrs.uy,
                size_bytes,
            );
            if status != CUDA_SUCCESS {
                return Err(format!("Failed to copy uy to host: {:?}", status));
            }
        }

        // Synchronize to ensure copy is complete
        self.stream.synchronize()
            .map_err(|e| format!("Failed to synchronize stream: {}", e))?;

        Ok((ux_host, uy_host))
    }

    /// Copy density data from GPU to host memory for visualization
    pub fn copy_density_to_host(&self, state: &LBMCudaState) -> Result<Vec<f32>, String> {
        let mut rho_host = vec![0.0f32; state.nx * state.ny];

        // Use raw CUDA driver API for memory copy
        unsafe {
            use cudarc::driver::sys::*;

            let size_bytes = (state.nx * state.ny * std::mem::size_of::<f32>()) as usize;

            // Copy rho data
            let status = cuMemcpyDtoH_v2(
                rho_host.as_mut_ptr() as *mut std::ffi::c_void,
                state.ptrs.rho,
                size_bytes,
            );
            if status != CUDA_SUCCESS {
                return Err(format!("Failed to copy rho to host: {:?}", status));
            }
        }

        // Synchronize to ensure copy is complete
        self.stream.synchronize()
            .map_err(|e| format!("Failed to synchronize stream: {}", e))?;

        Ok(rho_host)
    }

    /// Compute velocity magnitude on host from velocity components
    pub fn compute_velocity_magnitude_host(ux: &[f32], uy: &[f32]) -> Vec<f32> {
        ux.iter()
            .zip(uy.iter())
            .map(|(u, v)| (u * u + v * v).sqrt())
            .collect()
    }

    /// Run a complete LBM simulation step (legacy method)
    pub fn simulation_step(&self, state: &LBMCudaState, params: &LBMParams) -> Result<(), String> {
        self.launch_collision(state, params)?;
        self.launch_streaming(state)?;
        self.launch_boundary(state, params)?;
        self.launch_macroscopic(state)?;
        Ok(())
    }

    /// Run FluidX3D stream-collide step (high performance)
    pub fn fluidx3d_simulation_step(&self, state: &LBMCudaState, params: &LBMParams, time_step: u64) -> Result<(), String> {
        if let Some(ref stream_collide_kernel) = self.kernels.stream_collide {
            let launch_cfg = Self::get_2d_launch_config(state.nx, state.ny);

            unsafe {
                self.stream.launch_builder(stream_collide_kernel)
                    .arg(&state.ptrs.f)
                    .arg(&state.ptrs.rho)
                    .arg(&state.ptrs.ux)
                    .arg(&state.ptrs.uy)
                    .arg(&state.ptrs.solidity)
                    .arg(&(state.nx as i32))
                    .arg(&(state.ny as i32))
                    .arg(&(params.tau as f32))
                    .arg(&(params.inlet_velocity as f32))
                    .arg(&time_step)
                    .launch(launch_cfg)
                    .map_err(|e| format!("FluidX3D stream-collide kernel error: {}", e))?;
            }
            Ok(())
        } else {
            Err("FluidX3D stream-collide kernel not available".to_string())
        }
    }

    /// Update time step for Esoteric Pull pattern
    pub fn update_timestep(&self) -> Result<(), String> {
        if let Some(ref update_kernel) = self.kernels.update_timestep {
            let launch_cfg = LaunchConfig {
                grid_dim: (1, 1, 1),
                block_dim: (1, 1, 1),
                shared_mem_bytes: 0,
            };

            unsafe {
                self.stream.launch_builder(update_kernel)
                    .launch(launch_cfg)
                    .map_err(|e| format!("Update timestep kernel error: {}", e))?;
            }
            Ok(())
        } else {
            Err("Update timestep kernel not available".to_string())
        }
    }

    /// Calculate 2D launch configuration for kernels
    fn get_2d_launch_config(nx: usize, ny: usize) -> LaunchConfig {
        let block_size = 16;
        let grid_x = (nx + block_size - 1) / block_size;
        let grid_y = (ny + block_size - 1) / block_size;

        LaunchConfig {
            grid_dim: (grid_x as u32, grid_y as u32, 1),
            block_dim: (block_size as u32, block_size as u32, 1),
            shared_mem_bytes: 0,
        }
    }

    /// Register OpenGL buffer for CUDA interop (for visualization)
    pub fn register_gl_buffer(&self, gl_buffer: glow::Buffer) -> Result<CUgraphicsResource, String> {
        unsafe {
            let mut resource: CUgraphicsResource = std::ptr::null_mut();
            let result = cuda_gl_interop::cuGraphicsGLRegisterBuffer(
                &mut resource,
                gl_buffer.0.get(),
                CU_GRAPHICS_REGISTER_FLAGS_NONE as u32,
            );

            if result != CUDA_SUCCESS {
                return Err(format!("Failed to register GL buffer: {:?}", result));
            }

            Ok(resource)
        }
    }
}

/// High-level function to run a complete CUDA LBM simulation
pub fn cuda_solve(params: &LBMParams) -> Result<(), String> {
    println!("Initializing CUDA LBM solver...");

    // Create CUDA device
    let device = CudaLBMDevice::new()?;
    println!("CUDA device initialized successfully");

    // Allocate memory
    let state = device.allocate_lbm_memory(params.nx, params.ny, params.nl)?;
    println!("Memory allocated: {}x{} grid with {} directions", params.nx, params.ny, params.nl);

    // Initialize simulation
    device.launch_init(&state, params)?;
    println!("Simulation initialized");

    // Run simulation
    println!("Running {} timesteps...", params.nt);

    // Determine progress reporting frequency
    let report_interval = if params.nt <= 10 {
        1  // Report every step for very small simulations
    } else if params.nt <= 1000 {
        std::cmp::max(1, params.nt / 10)  // Report ~10 times
    } else {
        1000  // Report every 1000 steps for large simulations
    };

    for step in 0..params.nt {
        device.simulation_step(&state, params)?;

        // Print progress at intervals or for the final step
        if step % report_interval == 0 || step == params.nt - 1 {
            println!("Step {}/{}", step + 1, params.nt);
        }
    }

    println!("CUDA LBM simulation completed successfully!");
    Ok(())
}

/// High-level function to run FluidX3D CUDA LBM simulation with maximum performance
pub fn fluidx3d_cuda_solve(params: &LBMParams) -> Result<(), String> {
    println!("Initializing FluidX3D CUDA LBM solver...");
    println!("Using Esoteric Pull streaming for maximum performance");

    // Create CUDA device with FluidX3D kernels
    let device = CudaLBMDevice::new_fluidx3d()?;
    println!("FluidX3D CUDA device initialized successfully");

    // Allocate memory
    let state = device.allocate_lbm_memory(params.nx, params.ny, params.nl)?;
    println!("Memory allocated: {}x{} grid with {} directions", params.nx, params.ny, params.nl);

    // Calculate performance metrics
    let total_lattice_points = params.nx * params.ny;
    let reynolds_number = params.inlet_velocity * (params.ny as f64 / 20.0) / ((params.tau - 0.5) / 3.0);
    println!("Reynolds number: {:.2}", reynolds_number);

    // Initialize simulation
    device.launch_init(&state, params)?;
    println!("FluidX3D simulation initialized");

    // Run simulation with performance monitoring
    println!("Running {} timesteps with FluidX3D Esoteric Pull...", params.nt);

    let start_time = std::time::Instant::now();

    // Determine progress reporting frequency
    let report_interval = if params.nt <= 10 {
        1  // Report every step for very small simulations
    } else if params.nt <= 1000 {
        std::cmp::max(1, params.nt / 10)  // Report ~10 times
    } else {
        1000  // Report every 1000 steps for large simulations
    };

    for step in 0..params.nt {
        // Use FluidX3D high-performance kernel
        device.fluidx3d_simulation_step(&state, params, step as u64 + 1)?;

        // Print progress at intervals or for the final step
        if step % report_interval == 0 || step == params.nt - 1 {
            let elapsed = start_time.elapsed();
            let mlups = ((step + 1) * total_lattice_points) as f64 / elapsed.as_secs_f64() / 1e6;
            println!("Step {}/{} - {:.1} MLUPS", step + 1, params.nt, mlups);
        }
    }

    let total_elapsed = start_time.elapsed();
    let final_mlups = (params.nt * total_lattice_points) as f64 / total_elapsed.as_secs_f64() / 1e6;

    println!("FluidX3D CUDA LBM simulation completed successfully!");
    println!("Total time: {:.2?}", total_elapsed);
    println!("Average performance: {:.1} MLUPS", final_mlups);
    println!("Memory bandwidth efficiency: ~{:.1}%", (final_mlups / 1000.0).min(100.0));

    Ok(())
}

