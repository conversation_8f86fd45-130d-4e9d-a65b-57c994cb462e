use crate::docking_app::DockingApp;
use crate::tabs::{
    CodeEditorTab, JsonTreeTab, LBMCudaTab, LBMVelocityTab, PlotTab, StlRenderTab, TableTab,
    ValurileTab,
};
use eframe::{CreationContext, NativeOptions};
use egui_dock::DockState;
use serde_json::json;

use egui_logger;

pub fn editor() {
    egui_logger::builder().init().unwrap();
    let native_options = NativeOptions::default();

    let result = eframe::run_native(
        "My Docking App",
        native_options,
        Box::new(|cc: &CreationContext| {
            let mut initial_tabs = vec![
                ValurileTab::CodeEditor(CodeEditorTab {
                    title: "CodeEditor".into(),
                    code: "def main():\n    pass\n".into(),
                }),
                ValurileTab::JsonTree(JsonTreeTab {
                    title: "JsonTree".into(),
                    json: json!({ "foo": "bar", "fizz": [1, 2, 3] }),
                }),
                ValurileTab::Table(TableTab {
                    title: "Table".into(),
                    table: vec![vec!["A".into(), "B".into()], vec!["C".into(), "D".into()]],
                }),
                ValurileTab::Plot(PlotTab {
                    title: "Plot".into(),
                    plot: "my_plot".into(),
                }),
            ];

            let default_params = valurile_solver::lbm::data::LBMParams::default();

            if let Some(gl_arc) = cc.gl.as_ref() {
                initial_tabs.push(ValurileTab::LBMVelocity(LBMVelocityTab::new(
                    gl_arc,
                    default_params.clone(),
                )));
            }

            let higher_reynolds_params = valurile_solver::lbm::data::LBMParams {
                nx: 400,
                ny: 400,
                tau: 0.52,
                inlet_velocity: 0.15,
                ..default_params
            };

            if let Some(gl_arc) = cc.gl.as_ref() {
                initial_tabs.push(ValurileTab::StlTab(StlRenderTab::new(
                    &gl_arc.clone(),
                    "static/models/eiffel_tower.stl",
                    100,
                )));
            }

            if let Some(gl_arc) = cc.gl.as_ref() {
                initial_tabs.push(ValurileTab::LBMCuda(LBMCudaTab::new(
                    gl_arc,
                    higher_reynolds_params.clone(),
                )));
            }

            let app: DockingApp<ValurileTab> =
                DockingApp::new(initial_tabs, None::<fn(&mut DockState<ValurileTab>)>);

            Ok(Box::new(app))
        }),
    );

    if let Err(e) = result {
        eprintln!("Failed to run docking app: {e}");
    }
}
