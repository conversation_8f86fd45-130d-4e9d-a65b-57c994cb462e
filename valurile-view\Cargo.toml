[package]
name = "valurile-view"
version = "0.1.0"
edition = "2021"

[dependencies]
anyhow = "1.0.95"
eframe = { workspace = true }
egui = { workspace = true }
egui_code_editor = "0.2.13"
egui_dock = "0.16.0"
egui_json_tree = "0.11.0"
egui_plot = "0.32.1"
serde_json = "1.0.134"
enum_dispatch = "0.3.13"
serde = { version = "1.0.217", features = ["derive"] }
glow = { workspace = true }
log = "0.4.22"
egui_glow = { workspace = true }
egui_logger = "0.7.0"
bytemuck = { workspace = true }
cudarc = { workspace = true }

valurile-core = { path = "../valurile-core" }
valurile-solver = { path = "../valurile-solver" }
stl_io = "0.8.3"

[lib]
path = "src/lib.rs"
