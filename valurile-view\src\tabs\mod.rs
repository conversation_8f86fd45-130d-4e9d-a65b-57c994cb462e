use enum_dispatch::enum_dispatch;
use eframe::egui;

/// The core trait that every tab implements.
#[enum_dispatch]
pub trait DockTab {
    fn title(&self) -> &str;
    fn ui(&mut self, ui: &mut egui::Ui);
    fn closeable(&mut self) -> bool;
}

// Re-export each tab type and the enum in this module
mod code_editor_tab;
mod json_tree_tab;
mod table_tab;
mod plot_tab;
mod lbm_velocity_tab;
mod stl_tab;
mod lbm_cuda;
mod lbm_wgpu;
mod simple_lbm_wgpu;

pub use code_editor_tab::CodeEditorTab;
pub use json_tree_tab::JsonTreeTab;
pub use table_tab::TableTab;
pub use plot_tab::PlotTab;
pub use lbm_velocity_tab::LBMVelocityTab;
pub use stl_tab::StlRenderTab;
pub use lbm_cuda::LBMCudaTab;
pub use lbm_wgpu::LBMWgpuTab;
pub use simple_lbm_wgpu::SimpleLBMWgpuTab;

#[enum_dispatch(DockTab)]
pub enum ValurileTab {
    CodeEditor(CodeEditorTab),
    JsonTree(JsonTreeTab),
    Table(TableTab),
    Plot(PlotTab),
    LBMVelocity(LBMVelocityTab),
    StlTab(StlRenderTab),
    LBMCuda(LBMCudaTab),
    LBMWgpu(LBMWgpuTab),
    SimpleLBMWgpu(SimpleLBMWgpuTab),
}
