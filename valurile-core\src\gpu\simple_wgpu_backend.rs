use wgpu::{
    <PERSON><PERSON>, Queue, Texture, TextureView, TextureFormat, Buffer, BufferUsages,
};
use valurile_solver::lbm::data::LBMParams;
use valurile_solver::lbm::cuda_solver::{<PERSON>udaLBMDevice, LBMCudaState};
use crate::gpu::wgpu_texture_renderer::WgpuTextureRenderer;

/// Convert velocity magnitude (0.0 to 1.0) to RGB color
fn velocity_to_color(velocity: f32) -> (f32, f32, f32) {
    let v = velocity.clamp(0.0, 1.0);

    if v < 0.25 {
        // Blue to cyan
        let t = v * 4.0;
        (0.0, t, 1.0)
    } else if v < 0.5 {
        // <PERSON>an to green
        let t = (v - 0.25) * 4.0;
        (0.0, 1.0, 1.0 - t)
    } else if v < 0.75 {
        // Green to yellow
        let t = (v - 0.5) * 4.0;
        (t, 1.0, 0.0)
    } else {
        // Yellow to red
        let t = (v - 0.75) * 4.0;
        (1.0, 1.0 - t, 0.0)
    }
}

/// Simplified wgpu-based LBM simulator focused on getting basic functionality working
pub struct SimpleLBMWgpuSimulator {
    // Core components
    device: Device,
    queue: Queue,

    // CUDA simulation
    cuda_device: CudaLBMDevice,
    cuda_state: Option<LBMCudaState>,

    // Visualization
    visualization_texture: Option<Texture>,
    visualization_texture_view: Option<TextureView>,
    staging_buffer: Option<Buffer>,

    // Simulation state
    params: LBMParams,
    width: u32,
    height: u32,
    time_step: u64,
    is_initialized: bool,
}

impl SimpleLBMWgpuSimulator {
    /// Create a new simplified wgpu-based LBM simulator
    pub async fn new(
        params: LBMParams,
        width: u32,
        height: u32,
        kernel_source: &str,
    ) -> Result<Self, String> {
        // Initialize wgpu with minimal setup
        let instance = wgpu::Instance::new(wgpu::InstanceDescriptor {
            backends: wgpu::Backends::PRIMARY,
            ..Default::default()
        });

        let adapter = instance
            .request_adapter(&wgpu::RequestAdapterOptions {
                power_preference: wgpu::PowerPreference::HighPerformance,
                compatible_surface: None,
                force_fallback_adapter: false,
            })
            .await
            .ok_or("Failed to find suitable adapter")?;

        let (device, queue) = adapter
            .request_device(
                &wgpu::DeviceDescriptor {
                    label: Some("LBM Device"),
                    required_features: wgpu::Features::empty(),
                    required_limits: wgpu::Limits::default(),
                    memory_hints: wgpu::MemoryHints::Performance,
                },
                None,
            )
            .await
            .map_err(|e| format!("Failed to create device: {}", e))?;

        // Initialize CUDA device
        let cuda_device = CudaLBMDevice::new_with_kernel_source(kernel_source)?;

        Ok(Self {
            device,
            queue,
            cuda_device,
            cuda_state: None,
            visualization_texture: None,
            visualization_texture_view: None,
            staging_buffer: None,
            params,
            width,
            height,
            time_step: 0,
            is_initialized: false,
        })
    }

    /// Initialize the simulation
    pub fn initialize(&mut self) -> Result<(), String> {
        // Allocate CUDA memory
        let state = self.cuda_device.allocate_lbm_memory(
            self.params.nx,
            self.params.ny,
            self.params.nl,
        )?;

        // Initialize simulation
        self.cuda_device.launch_init(&state, &self.params)?;
        self.cuda_state = Some(state);

        // Create visualization texture
        let texture = self.device.create_texture(&wgpu::TextureDescriptor {
            label: Some("LBM Visualization Texture"),
            size: wgpu::Extent3d {
                width: self.width,
                height: self.height,
                depth_or_array_layers: 1,
            },
            mip_level_count: 1,
            sample_count: 1,
            dimension: wgpu::TextureDimension::D2,
            format: TextureFormat::Rgba8Unorm,
            usage: wgpu::TextureUsages::TEXTURE_BINDING
                | wgpu::TextureUsages::COPY_DST
                | wgpu::TextureUsages::RENDER_ATTACHMENT,
            view_formats: &[],
        });

        let texture_view = texture.create_view(&wgpu::TextureViewDescriptor::default());

        // Create staging buffer for CPU-GPU data transfer
        let staging_buffer = self.device.create_buffer(&wgpu::BufferDescriptor {
            label: Some("LBM Staging Buffer"),
            size: (self.width * self.height * 4) as u64, // RGBA8
            usage: BufferUsages::COPY_SRC | BufferUsages::MAP_WRITE,
            mapped_at_creation: false,
        });

        self.visualization_texture = Some(texture);
        self.visualization_texture_view = Some(texture_view);
        self.staging_buffer = Some(staging_buffer);
        self.is_initialized = true;

        Ok(())
    }

    /// Run one simulation timestep
    pub fn step(&mut self) -> Result<(), String> {
        if !self.is_initialized {
            return Err("Simulator not initialized".to_string());
        }

        if let Some(state) = &self.cuda_state {
            self.time_step += 1;

            // Run LBM simulation step
            self.cuda_device.launch_collision(state, &self.params)?;
            self.cuda_device.launch_streaming(state)?;
            self.cuda_device.launch_boundary(state, &self.params)?;
            self.cuda_device.launch_macroscopic(state)?;

            // Debug output every 60 steps
            if self.time_step % 60 == 0 {
                println!("LBM Step {}: CUDA kernels executed successfully", self.time_step);
            }
        }
        Ok(())
    }

    /// Update visualization with real CUDA LBM data
    pub fn update_visualization(&mut self) -> Result<(), String> {
        if !self.is_initialized {
            return Ok(());
        }

        if let Some(state) = &self.cuda_state {
            // Copy velocity data from CUDA to host
            let (ux_host, uy_host) = self.cuda_device.copy_velocity_to_host(state)?;

            // Compute velocity magnitude
            let velocity_magnitude = CudaLBMDevice::compute_velocity_magnitude_host(&ux_host, &uy_host);

            // Find max velocity for normalization
            let max_velocity = velocity_magnitude.iter()
                .fold(0.0f32, |max, &val| if val > max { val } else { max });

            // Debug output every 60 frames for visualization updates
            if self.time_step % 60 == 0 {
                let avg_velocity = velocity_magnitude.iter().sum::<f32>() / velocity_magnitude.len() as f32;
                println!("Visualization Update Step {}: max_vel={:.6}, avg_vel={:.6}", self.time_step, max_velocity, avg_velocity);
            }

            // Convert to RGBA texture data
            let mut data = vec![0u8; (self.width * self.height * 4) as usize];

            for y in 0..self.height {
                for x in 0..self.width {
                    let sim_idx = (y * self.width + x) as usize;
                    let tex_idx = ((y * self.width + x) * 4) as usize;

                    // Get normalized velocity magnitude (0.0 to 1.0)
                    let vel_norm = if max_velocity > 0.0 {
                        (velocity_magnitude[sim_idx] / max_velocity).min(1.0)
                    } else {
                        0.0
                    };

                    // Convert to color using a simple blue-to-red colormap
                    let (r, g, b) = velocity_to_color(vel_norm);

                    data[tex_idx] = (r * 255.0) as u8;     // R
                    data[tex_idx + 1] = (g * 255.0) as u8; // G
                    data[tex_idx + 2] = (b * 255.0) as u8; // B
                    data[tex_idx + 3] = 255;               // A
                }
            }

            // Upload data to texture
            if let Some(texture) = &self.visualization_texture {
                self.queue.write_texture(
                    wgpu::ImageCopyTexture {
                        texture,
                        mip_level: 0,
                        origin: wgpu::Origin3d::ZERO,
                        aspect: wgpu::TextureAspect::All,
                    },
                    &data,
                    wgpu::ImageDataLayout {
                        offset: 0,
                        bytes_per_row: Some(self.width * 4),
                        rows_per_image: Some(self.height),
                    },
                    wgpu::Extent3d {
                        width: self.width,
                        height: self.height,
                        depth_or_array_layers: 1,
                    },
                );
            }
        }

        Ok(())
    }

    /// Get the visualization texture view for rendering
    pub fn get_texture_view(&self) -> Option<&TextureView> {
        self.visualization_texture_view.as_ref()
    }

    /// Get the current visualization data as RGBA bytes for egui display
    pub fn get_visualization_image_data(&self) -> Option<Vec<u8>> {
        if !self.is_initialized {
            return None;
        }

        if let Some(state) = &self.cuda_state {
            // Copy velocity and solidity data from CUDA to host
            if let (Ok((ux_host, uy_host)), Ok(_solidity_host)) = (
                self.cuda_device.copy_velocity_to_host(state),
                self.cuda_device.copy_density_to_host(state) // We'll use this to get solidity data
            ) {
                // Compute velocity magnitude
                let velocity_magnitude = CudaLBMDevice::compute_velocity_magnitude_host(&ux_host, &uy_host);

                // Find max velocity for normalization
                let max_velocity = velocity_magnitude.iter()
                    .fold(0.0f32, |max, &val| if val > max { val } else { max });



                // Convert to RGBA texture data
                let mut data = vec![0u8; (self.width * self.height * 4) as usize];

                for y in 0..self.height {
                    for x in 0..self.width {
                        let sim_idx = (y * self.width + x) as usize;
                        let tex_idx = ((y * self.width + x) * 4) as usize;

                        // Check if this is inside the cylinder (approximate)
                        let cylinder_x = self.width as f32 / 3.0;
                        let cylinder_y = self.height as f32 / 2.0;
                        let cylinder_radius = self.height as f32 / 40.0; // diameter/2 = ny/20/2 = ny/40
                        let dx = x as f32 - cylinder_x;
                        let dy = y as f32 - cylinder_y;
                        let is_cylinder = (dx * dx + dy * dy) < (cylinder_radius * cylinder_radius);

                        if is_cylinder {
                            // Show cylinder as white
                            data[tex_idx] = 255;     // R
                            data[tex_idx + 1] = 255; // G
                            data[tex_idx + 2] = 255; // B
                            data[tex_idx + 3] = 255; // A
                        } else {
                            // Get normalized velocity magnitude (0.0 to 1.0)
                            let vel_norm = if max_velocity > 0.0 {
                                (velocity_magnitude[sim_idx] / max_velocity).min(1.0)
                            } else {
                                0.0
                            };

                            // Convert to color using a simple blue-to-red colormap
                            let (r, g, b) = velocity_to_color(vel_norm);

                            data[tex_idx] = (r * 255.0) as u8;     // R
                            data[tex_idx + 1] = (g * 255.0) as u8; // G
                            data[tex_idx + 2] = (b * 255.0) as u8; // B
                            data[tex_idx + 3] = 255;               // A
                        }
                    }
                }

                return Some(data);
            }
        }

        None
    }

    /// Check if the simulator is initialized
    pub fn is_initialized(&self) -> bool {
        self.is_initialized
    }

    /// Get current simulation parameters
    pub fn params(&self) -> &LBMParams {
        &self.params
    }

    /// Update simulation parameters
    pub fn set_params(&mut self, params: LBMParams) -> bool {
        let needs_reinit = params.nx != self.params.nx || params.ny != self.params.ny;
        self.params = params;
        if needs_reinit {
            self.is_initialized = false;
        }
        needs_reinit
    }

    /// Get wgpu device reference
    pub fn device(&self) -> &Device {
        &self.device
    }

    /// Get wgpu queue reference
    pub fn queue(&self) -> &Queue {
        &self.queue
    }

    /// Get current time step
    pub fn time_step(&self) -> u64 {
        self.time_step
    }

    /// Reset simulation
    pub fn reset(&mut self) -> Result<(), String> {
        self.time_step = 0;
        if let Some(state) = &self.cuda_state {
            self.cuda_device.launch_init(state, &self.params)?;
        }
        Ok(())
    }
}
