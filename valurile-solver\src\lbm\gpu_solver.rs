use ndarray::{s, Array2, <PERSON>rray3, Axis, Zip};
use ndarray_rand::{rand_distr::Normal, RandomExt};

use rand::rngs::StdRng;
use rand::SeedableRng;

use ocl::{flags, Buffer, Context, Kernel, Program, Queue};

use colored::*;

use indicatif::ProgressIterator;

use super::data::*;
use super::util::*;

pub fn gpu_solve(params: &LBMParams) {
    println!("Starting GPU LBM Solver With OpenCL");

    // print_gpu_info();

    // Initialize OpenCL
    let context = Context::builder().build().unwrap();
    let queue = Queue::new(&context, context.devices()[0], None).unwrap();

    // Create OpenCL buffers
    let buffer_len = params.ny * params.nx * params.nl;
    let mut f_buffer = Buffer::<f64>::builder()
        .queue(queue.clone())
        .flags(flags::MEM_READ_WRITE)
        .len(buffer_len)
        .build()
        .unwrap();

    let mut temp_f_buffer = Buffer::<f64>::builder()
        .queue(queue.clone())
        .flags(flags::MEM_READ_WRITE)
        .len(buffer_len)
        .build()
        .unwrap();

    let rho_buffer = Buffer::<f64>::builder()
        .queue(queue.clone())
        .flags(flags::MEM_READ_WRITE)
        .len(params.ny * params.nx)
        .build()
        .unwrap();

    let ux_buffer = Buffer::<f64>::builder()
        .queue(queue.clone())
        .flags(flags::MEM_READ_WRITE)
        .len(params.ny * params.nx)
        .build()
        .unwrap();

    let uy_buffer = Buffer::<f64>::builder()
        .queue(queue.clone())
        .flags(flags::MEM_READ_WRITE)
        .len(params.ny * params.nx)
        .build()
        .unwrap();

    let cxs_buffer = Buffer::<i32>::builder()
        .queue(queue.clone())
        .flags(flags::MEM_READ_ONLY)
        .len(params.nl)
        .copy_host_slice(&params.cxs)
        .build()
        .unwrap();

    let cys_buffer = Buffer::<i32>::builder()
        .queue(queue.clone())
        .flags(flags::MEM_READ_ONLY)
        .len(params.nl)
        .copy_host_slice(&params.cys)
        .build()
        .unwrap();

    let weights_buffer = Buffer::<f64>::builder()
        .queue(queue.clone())
        .flags(flags::MEM_READ_ONLY)
        .len(params.nl)
        .copy_host_slice(&params.weights)
        .build()
        .unwrap();

    let solidity_buffer = Buffer::<f64>::builder()
        .queue(queue.clone())
        .flags(flags::MEM_READ_ONLY)
        .len(params.ny * params.nx)
        .build()
        .unwrap();

    // Create and build the OpenCL programs
    let cs_program = Program::builder()
        .src(include_str!("ocl/collision_and_streaming.cl"))
        .devices(&context.devices())
        .build(&context)
        .unwrap();
    let bc_program = Program::builder()
        .src(include_str!("ocl/apply_boundary_conditions.cl"))
        .devices(&context.devices())
        .build(&context)
        .unwrap();
    let cm_program = Program::builder()
        .src(include_str!("ocl/compute_macroscopic.cl"))
        .devices(&context.devices())
        .build(&context)
        .unwrap();

    // Initialize simulation state
    let mut state = initialize_simulation_state(params);
    let reynolds_number = calculate_reynolds_number(params);
    println!("Reynolds number: {}", reynolds_number);

    // Copy initial data to device
    f_buffer.write(state.f.as_slice().unwrap()).enq().unwrap();
    rho_buffer
        .write(state.rho.as_slice().unwrap())
        .enq()
        .unwrap();
    ux_buffer.write(state.ux.as_slice().unwrap()).enq().unwrap();
    uy_buffer.write(state.uy.as_slice().unwrap()).enq().unwrap();
    solidity_buffer
        .write(state.solidity.as_slice().unwrap())
        .enq()
        .unwrap();

    // Create the kernels
    let collision_streaming_kernel = Kernel::builder()
        .program(&cs_program)
        .name("collision_and_streaming")
        .queue(queue.clone())
        .global_work_size([params.ny as usize, params.nx as usize])
        .arg(&f_buffer) // f_in
        .arg(&temp_f_buffer) // f_out
        .arg(&solidity_buffer)
        .arg(&cxs_buffer)
        .arg(&cys_buffer)
        .arg(&weights_buffer)
        .arg(params.tau)
        .arg(params.nx as i32)
        .arg(params.ny as i32)
        .arg(params.nl as i32)
        .build()
        .unwrap();

    let boundary_kernel = Kernel::builder()
        .program(&bc_program)
        .name("apply_boundary_conditions")
        .queue(queue.clone())
        .global_work_size([params.ny as usize, params.nx as usize])
        .arg(&temp_f_buffer) // f
        .arg(&solidity_buffer)
        .arg(&cxs_buffer)
        .arg(&cys_buffer)
        .arg(params.inlet_velocity)
        .arg(params.nx as i32)
        .arg(params.ny as i32)
        .arg(params.nl as i32)
        .build()
        .unwrap();

    let macroscopic_kernel = Kernel::builder()
        .program(&cm_program)
        .name("compute_macroscopic")
        .queue(queue.clone())
        .global_work_size([params.ny as usize, params.nx as usize])
        .arg(&temp_f_buffer) // f
        .arg(&rho_buffer)
        .arg(&ux_buffer)
        .arg(&uy_buffer)
        .arg(&solidity_buffer)
        .arg(&cxs_buffer)
        .arg(&cys_buffer)
        .arg(params.nx as i32)
        .arg(params.ny as i32)
        .arg(params.nl as i32)
        .build()
        .unwrap();

    // Main simulation loop
    for it in (0..params.nt).progress() {
        // Collision and Streaming
        // Apply Boundary Conditions
        // Compute Macroscopic Variables
        unsafe {
            collision_streaming_kernel.enq().unwrap();
            boundary_kernel.enq().unwrap();
            macroscopic_kernel.enq().unwrap();
        }

        // Swap f_buffer and temp_f_buffer
        std::mem::swap(&mut f_buffer, &mut temp_f_buffer);

        // Update kernel arguments for f_in and f_out
        collision_streaming_kernel.set_arg(0, &f_buffer).unwrap(); // f_in
        collision_streaming_kernel
            .set_arg(1, &temp_f_buffer)
            .unwrap(); // f_out

        // Ensure boundary_kernel operates on the correct f
        boundary_kernel.set_arg(0, &temp_f_buffer).unwrap();

        // Ensure macroscopic_kernel operates on the correct f
        macroscopic_kernel.set_arg(0, &temp_f_buffer).unwrap();

        // Export results periodically
        if (it + 1) % 50 == 0 {
            // Read data back to host
            f_buffer
                .read(state.f.as_slice_mut().unwrap())
                .enq()
                .unwrap();
            rho_buffer
                .read(state.rho.as_slice_mut().unwrap())
                .enq()
                .unwrap();
            ux_buffer
                .read(state.ux.as_slice_mut().unwrap())
                .enq()
                .unwrap();
            uy_buffer
                .read(state.uy.as_slice_mut().unwrap())
                .enq()
                .unwrap();

            // Ensure all commands are finished before exporting
            queue.finish().unwrap();

            // Export results
            export_results(&state, params, it);
        }
    }

    println!("{}", "GPU LBM Solver Finished".green().bold());
}

fn initialize_simulation_state(params: &LBMParams) -> SimulationState {
    let mut rng = StdRng::seed_from_u64(42);
    let mut f = Array3::<f64>::zeros((params.ny, params.nx, params.nl));

    // 1. Initialize equilibrium distributions
    for y in 0..params.ny {
        for x in 0..params.nx {
            for i in 0..params.nl {
                f[[y, x, i]] = params.weights[i] * params.rho0;
            }
        }
    }

    // 2. Add small perturbations to avoid symmetry
    f = &f
        + &Array3::random_using(
            (params.ny, params.nx, params.nl),
            Normal::new(0.0, 0.001).unwrap(),
            &mut rng,
        );

    // 3. Apply inlet velocity once
    for y in 0..params.ny {
        f[[y, 0, 1]] += params.weights[1] * 3.0 * params.inlet_velocity;
        f[[y, 0, 5]] += params.weights[5] * 3.0 * params.inlet_velocity;
        f[[y, 0, 8]] += params.weights[8] * 3.0 * params.inlet_velocity;
    }

    // 4. Scale to maintain rho0
    let rho = f.sum_axis(Axis(2));
    for i in 0..params.nl {
        Zip::from(f.slice_mut(s![.., .., i]))
            .and(&rho)
            .for_each(|f_i, &rho_xy| {
                *f_i *= params.rho0 / rho_xy;
                // Prevent negative or non-finite f's
                if *f_i < 0.0 || !f_i.is_finite() {
                    *f_i = 1e-8;
                }
            });
    }

    // Initialize cylinder solidity
    let solidity = initialize_solidity(params);

    SimulationState {
        f,
        rho,
        ux: Array2::<f64>::zeros((params.ny, params.nx)),
        uy: Array2::<f64>::zeros((params.ny, params.nx)),
        solidity,
    }
}

fn initialize_solidity(params: &LBMParams) -> Array2<f64> {
    let mut solidity = Array2::<f64>::zeros((params.ny, params.nx));
    let cylinder_diameter = params.ny as f64 / 20.0;
    for y in 0..params.ny {
        for x in 0..params.nx {
            if ((x as f64 - params.nx as f64 / 3.0).powi(2)
                + (y as f64 - params.ny as f64 / 2.0).powi(2))
                < (cylinder_diameter / 2.0).powi(2)
            {
                solidity[[y, x]] = 1.0;
            }
        }
    }
    solidity
}
