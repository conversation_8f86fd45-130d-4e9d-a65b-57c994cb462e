use clap::Parser;
use valurile_view::editor;
use valurile_solver::lbm;

#[derive(Parser, Debug)]
#[command(author, version, about, long_about = None)]
struct Args {
    #[arg(long, default_value_t = 2500)]
    nx: usize,
    #[arg(long, default_value_t = 2500)]
    ny: usize,
    #[arg(long, default_value_t = 1.0)]
    rho_0: f64,
    #[arg(long, default_value_t = 0.6)]
    tau: f64,
    #[arg(long, default_value_t = 20000)]
    timesteps: usize,
    #[arg(long, default_value_t = 0.005)]
    inlet_velocity: f64,
    #[arg(long)]
    solve: bool,
    #[arg(long)]
    parallel: bool,
    #[arg(long)]
    gpu: bool,
    #[arg(long)]
    cuda: bool,
    #[arg(long)]
    fluidx3d: bool,
    #[arg(long)]
    opencl: bool,
}

fn main() {
    let args = Args::parse(); // Parse the command-line arguments

    if !args.solve {
        editor();
        return;
    }

    let params = lbm::data::LBMParams {
        nx: args.nx,
        ny: args.ny,
        nt: args.timesteps,
        nl: 9,
        rho0: args.rho_0,
        tau: args.tau,
        inlet_velocity: args.inlet_velocity,
        cxs: vec![0, 1, 0, -1, 0, 1, -1, -1, 1],
        cys: vec![0, 0, 1, 0, -1, 1, 1, -1, -1],
        weights: vec![
            4.0 / 9.0,
            1.0 / 9.0,
            1.0 / 9.0,
            1.0 / 9.0,
            1.0 / 9.0,
            1.0 / 36.0,
            1.0 / 36.0,
            1.0 / 36.0,
            1.0 / 36.0,
        ],
    };

    if !args.parallel && !args.gpu {
        lbm::solver::solve(&params);
    } else if !args.gpu {
        lbm::parallel_solver::parallel_solve(&params);
    } else if args.gpu && args.cuda {
        if args.fluidx3d {
            println!("FluidX3D CUDA Solver Starting...");
            match lbm::cuda_solver::fluidx3d_cuda_solve(&params) {
                Ok(()) => println!("FluidX3D CUDA Solve ran successfully."),
                Err(e) => println!("Failed to run FluidX3D cuda_solve: {}", e),
            }
        } else {
            println!("Standard CUDA Solver Starting...");
            match lbm::cuda_solver::cuda_solve(&params) {
                Ok(()) => println!("Standard CUDA Solve ran successfully."),
                Err(e) => println!("Failed to run standard cuda_solve: {}", e),
            }
        }
    } else {
        lbm::gpu_solver::gpu_solve(&params);
    }
}
