use crate::tabs::DockTab;
use eframe::egui;
use egui_plot::{Line, Plot, PlotPoints};

pub struct PlotTab {
    pub title: String,
    pub plot: String,
}

impl DockTab for PlotTab {
    fn title(&self) -> &str {
        &self.title
    }
    fn ui(&mut self, ui: &mut egui::Ui) {
        ui.heading("Plot");
        Plot::new("my_plot")
            .allow_zoom(false)
            .allow_drag(false)
            .show(ui, |plot_ui| {
                let sine_points = PlotPoints::from_explicit_callback(|x| x.sin(), .., 5000);
                plot_ui.line(Line::new("Sine", sine_points));
            });
    }
    fn closeable(&mut self) -> bool {
        false
    }
}
