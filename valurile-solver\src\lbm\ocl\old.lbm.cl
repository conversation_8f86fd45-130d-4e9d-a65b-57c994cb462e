#pragma OPENCL EXTENSION cl_khr_fp64 : enable

__kernel void lbm(
    __global double* f_in,
    __global double* f_out,
    __global double* rho,
    __global double* ux,
    __global double* uy,
    __global double* solidity,
    __constant int* cxs,
    __constant int* cys,
    __constant double* weights,
    double tau,
    double inlet_velocity,
    int nx,
    int ny,
    int nl
) {
    int y = get_global_id(0);
    int x = get_global_id(1);

    if (y >= ny || x >= nx) return;

    int idx = y * nx + x;
    int f_idx = idx * nl;

    // --- Read f_in into local variables ---
    double f_local[9];
    for (int i = 0; i < nl; i++) {
        f_local[i] = f_in[f_idx + i];
    }

    // --- Compute Macroscopic Variables ---
    double rho_val = 0.0;
    double ux_val = 0.0;
    double uy_val = 0.0;

    for (int i = 0; i < nl; i++) {
        double f_i = f_local[i];
        rho_val += f_i;
        ux_val += f_i * cxs[i];
        uy_val += f_i * cys[i];
    }

    // Avoid division by zero
    if (rho_val < 1e-8) {
        rho_val = 1e-8;
    }

    ux_val /= rho_val;
    uy_val /= rho_val;

    // No-slip condition inside the cylinder
    if (solidity[idx] > 0.0) {
        ux_val = 0.0;
        uy_val = 0.0;
    }

    double u_square = ux_val * ux_val + uy_val * uy_val;

    // --- Collision Step ---
    for (int i = 0; i < nl; i++) {
        double cu = cxs[i] * ux_val + cys[i] * uy_val;
        double feq = rho_val * weights[i] * (1.0 + 3.0 * cu + 4.5 * cu * cu - 1.5 * u_square);
        f_local[i] += (feq - f_local[i]) / tau;

        // Safeguard
        if (f_local[i] < 0.0) {
            f_local[i] = 1e-8;
        }
    }

    // --- Apply Boundary Conditions to f_local ---
    // Cylinder Boundary
    if (solidity[idx] > 0.0) {
        // Swap opposite directions
        double temp[9];
        for (int i = 0; i < nl; i++) {
            temp[i] = f_local[i];
        }
        f_local[1] = temp[3];
        f_local[2] = temp[4];
        f_local[3] = temp[1];
        f_local[4] = temp[2];
        f_local[5] = temp[7];
        f_local[6] = temp[8];
        f_local[7] = temp[5];
        f_local[8] = temp[6];
    }

    // Inlet Boundary
    if (x == 0) {
        double rho_inlet = f_local[0] + f_local[2] + f_local[4] + 2.0 * (f_local[3] + f_local[6] + f_local[7]);

        f_local[1] = f_local[3] + (2.0 / 3.0) * rho_inlet * inlet_velocity;
        f_local[5] = f_local[7] - 0.5 * (f_local[2] - f_local[4]) + (rho_inlet * inlet_velocity) / 6.0;
        f_local[8] = f_local[6] + 0.5 * (f_local[2] - f_local[4]) + (rho_inlet * inlet_velocity) / 6.0;
    }

    // Outlet Boundary
    if (x == nx - 1) {
        // Copy from previous cell in f_in
        int prev_idx = (y * nx + x - 1) * nl;
        for (int i = 0; i < nl; i++) {
            f_local[i] = f_in[prev_idx + i];
        }
    }

    // --- Compute Macroscopic Variables based on f_local ---
    rho_val = 0.0;
    ux_val = 0.0;
    uy_val = 0.0;

    for (int i = 0; i < nl; i++) {
        double f_i = f_local[i];
        rho_val += f_i;
        ux_val += f_i * cxs[i];
        uy_val += f_i * cys[i];
    }

    if (rho_val < 1e-8) {
        rho_val = 1e-8;
    }

    ux_val /= rho_val;
    uy_val /= rho_val;

    if (solidity[idx] > 0.0) {
        ux_val = 0.0;
        uy_val = 0.0;
    }

    rho[idx] = rho_val;
    ux[idx] = ux_val;
    uy[idx] = uy_val;

    // --- Streaming Step ---
    for (int i = 0; i < nl; i++) {
        int cy = cys[i];
        int cx = cxs[i];
        int new_y = (y + cy + ny) % ny;
        int new_x = (x + cx + nx) % nx;
        int dst_idx = (new_y * nx + new_x) * nl + i;
        f_out[dst_idx] = f_local[i];
    }
}

