use crate::tabs::DockTab;

use eframe::egui;
use eframe::egui_glow;
use egui::mutex::Mutex;
use egui_glow::glow;
use glow::HasContext as _;

use std::path::Path;
use std::sync::Arc;

use stl_io;

/// Read positions, normals, indices from an STL file
fn load_stl_data<P: AsRef<Path>>(
    path: P,
) -> Result<(Vec<f32>, Vec<f32>, Vec<u32>), Box<dyn std::error::Error>> {
    let mut file = std::fs::File::open(path)?;
    let mesh = stl_io::read_stl(&mut file)?;

    let mut positions = Vec::new();
    let mut normals = Vec::new();
    let mut indices = Vec::new();
    let mut current_index = 0;

    for face in &mesh.faces {
        let normal = face.normal;
        for &v_idx in &face.vertices {
            let vert = mesh.vertices[v_idx as usize];
            positions.extend_from_slice(&[vert[0], vert[1], vert[2]]);
            normals.extend_from_slice(&[normal[0], normal[1], normal[2]]);
            indices.push(current_index);
            current_index += 1;
        }
    }
    Ok((positions, normals, indices))
}

/// GPU struct for drawing an STL, plus a centered perfect cube with an internal grid
pub struct StlModel {
    /// Single shader program for everything (STL, bounding box, grid)
    pub program: glow::Program,

    // STL geometry
    pub stl_vao: glow::VertexArray,
    pub stl_vbo_positions: glow::Buffer,
    pub stl_vbo_normals: glow::Buffer,
    pub stl_ebo: glow::Buffer,
    pub stl_index_count: i32,

    // Cube bounding box geometry
    pub cube_vao: glow::VertexArray,
    pub cube_vbo_positions: glow::Buffer,
    pub cube_vbo_normals: glow::Buffer,
    pub cube_ebo: glow::Buffer,
    pub cube_index_count: i32,

    // Grid geometry
    pub grid_vao: glow::VertexArray,
    pub grid_vbo_positions: glow::Buffer,
    pub grid_vbo_normals: glow::Buffer,
    pub grid_ebo: glow::Buffer,
    pub grid_index_count: i32,

    /// Uniforms
    pub u_mvp_loc: Option<glow::UniformLocation>,
    pub u_use_normals_loc: Option<glow::UniformLocation>,
}

unsafe impl Send for StlModel {}
unsafe impl Sync for StlModel {}

impl StlModel {
    /// Build the model from the STL file, subdividing a perfect cube around stl center
    /// `cubes_per_dim`: how many subdivisions along each axis for the internal grid.
    pub fn new(gl: &glow::Context, path: &str, cubes_per_dim: u32) -> Option<Self> {
        unsafe {
            // 1) Load STL
            let (positions, normals, indices) = match load_stl_data(path) {
                Ok(d) => d,
                Err(e) => {
                    eprintln!("Failed to load STL: {}", e);
                    return None;
                }
            };

            // Compute STL bounding box min/max
            let mut min_x = f32::MAX;
            let mut min_y = f32::MAX;
            let mut min_z = f32::MAX;
            let mut max_x = f32::MIN;
            let mut max_y = f32::MIN;
            let mut max_z = f32::MIN;

            for chunk in positions.chunks_exact(3) {
                let (x, y, z) = (chunk[0], chunk[1], chunk[2]);
                if x < min_x {
                    min_x = x;
                }
                if y < min_y {
                    min_y = y;
                }
                if z < min_z {
                    min_z = z;
                }
                if x > max_x {
                    max_x = x;
                }
                if y > max_y {
                    max_y = y;
                }
                if z > max_z {
                    max_z = z;
                }
            }

            let dx = max_x - min_x;
            let dy = max_y - min_y;
            let dz = max_z - min_z;

            let cx = (min_x + max_x) * 0.5;
            let cy = (min_y + max_y) * 0.5;
            let cz = (min_z + max_z) * 0.5;

            // perfect cube of side length = largest_dim, centered at (cx, cy, cz).
            let largest_dim = dx.max(dy).max(dz);
            let half_dim = largest_dim * 0.5;

            // "Cube bounding box" corners:
            let cube_min_x = cx - half_dim;
            let cube_max_x = cx + half_dim;
            let cube_min_y = cy - half_dim;
            let cube_max_y = cy + half_dim;
            let cube_min_z = cz - half_dim;
            let cube_max_z = cz + half_dim;

            // Create and compile a single shader program used for everything
            let program = gl.create_program().ok()?;
            let vs_src = r#"#version 330
                layout(location=0) in vec3 a_pos;
                layout(location=1) in vec3 a_normal;

                uniform mat4 u_mvp;
                uniform bool useNormals;

                out vec3 v_normal;

                void main() {
                    if (useNormals) {
                        v_normal = a_normal;
                    } else {
                        // bounding box or grid lines
                        v_normal = vec3(0.0, 0.0, 0.0);
                    }
                    gl_Position = u_mvp * vec4(a_pos, 1.0);
                }
            "#;

            let fs_src = r#"#version 330
                in vec3 v_normal;
                out vec4 out_color;

                uniform bool useNormals;

                void main() {
                    if (useNormals) {
                        // STL pass -> normal-based color
                        out_color = vec4(abs(v_normal), 1.0);
                    } else {
                        // bounding box / grid lines -> white
                        out_color = vec4(1.0, 1.0, 1.0, 0.01);
                    }
                }
            "#;

            let vs = gl.create_shader(glow::VERTEX_SHADER).unwrap();
            gl.shader_source(vs, vs_src);
            gl.compile_shader(vs);
            if !gl.get_shader_compile_status(vs) {
                eprintln!("VS error: {}", gl.get_shader_info_log(vs));
                gl.delete_shader(vs);
                return None;
            }

            let fs = gl.create_shader(glow::FRAGMENT_SHADER).unwrap();
            gl.shader_source(fs, fs_src);
            gl.compile_shader(fs);
            if !gl.get_shader_compile_status(fs) {
                eprintln!("FS error: {}", gl.get_shader_info_log(fs));
                gl.delete_shader(fs);
                return None;
            }

            gl.attach_shader(program, vs);
            gl.attach_shader(program, fs);
            gl.link_program(program);
            if !gl.get_program_link_status(program) {
                eprintln!("Link error: {}", gl.get_program_info_log(program));
                return None;
            }
            gl.detach_shader(program, vs);
            gl.detach_shader(program, fs);
            gl.delete_shader(vs);
            gl.delete_shader(fs);

            let u_mvp_loc = gl.get_uniform_location(program, "u_mvp");
            let u_use_normals_loc = gl.get_uniform_location(program, "useNormals");

            // Build STL geometry VAO/VBO/EBO
            let stl_vao = gl.create_vertex_array().ok()?;
            gl.bind_vertex_array(Some(stl_vao));

            let stl_vbo_positions = gl.create_buffer().ok()?;
            gl.bind_buffer(glow::ARRAY_BUFFER, Some(stl_vbo_positions));
            gl.buffer_data_u8_slice(
                glow::ARRAY_BUFFER,
                bytemuck::cast_slice(&positions),
                glow::STATIC_DRAW,
            );
            gl.enable_vertex_attrib_array(0);
            gl.vertex_attrib_pointer_f32(0, 3, glow::FLOAT, false, 3 * 4, 0);

            let stl_vbo_normals = gl.create_buffer().ok()?;
            gl.bind_buffer(glow::ARRAY_BUFFER, Some(stl_vbo_normals));
            gl.buffer_data_u8_slice(
                glow::ARRAY_BUFFER,
                bytemuck::cast_slice(&normals),
                glow::STATIC_DRAW,
            );
            gl.enable_vertex_attrib_array(1);
            gl.vertex_attrib_pointer_f32(1, 3, glow::FLOAT, false, 3 * 4, 0);

            let stl_ebo = gl.create_buffer().ok()?;
            gl.bind_buffer(glow::ELEMENT_ARRAY_BUFFER, Some(stl_ebo));
            gl.buffer_data_u8_slice(
                glow::ELEMENT_ARRAY_BUFFER,
                bytemuck::cast_slice(&indices),
                glow::STATIC_DRAW,
            );

            let stl_index_count = indices.len() as i32;
            gl.bind_vertex_array(None);

            // Build the lines for the cube bounding box:
            let cube_positions: [f32; 24] = [
                // corners: (x, y, z)
                cube_min_x, cube_min_y, cube_min_z, // 0
                cube_min_x, cube_min_y, cube_max_z, // 1
                cube_min_x, cube_max_y, cube_min_z, // 2
                cube_min_x, cube_max_y, cube_max_z, // 3
                cube_max_x, cube_min_y, cube_min_z, // 4
                cube_max_x, cube_min_y, cube_max_z, // 5
                cube_max_x, cube_max_y, cube_min_z, // 6
                cube_max_x, cube_max_y, cube_max_z, // 7
            ];
            let cube_normals: [f32; 24] = [0.0; 24];
            let cube_indices: [u32; 24] = [
                // 12 edges
                0, 1, 0, 2, 0, 4, 1, 3, 1, 5, 2, 3, 2, 6, 3, 7, 4, 5, 4, 6, 5, 7, 6, 7,
            ];

            let cube_vao = gl.create_vertex_array().ok()?;
            gl.bind_vertex_array(Some(cube_vao));

            let cube_vbo_positions = gl.create_buffer().ok()?;
            gl.bind_buffer(glow::ARRAY_BUFFER, Some(cube_vbo_positions));
            gl.buffer_data_u8_slice(
                glow::ARRAY_BUFFER,
                bytemuck::cast_slice(&cube_positions),
                glow::STATIC_DRAW,
            );
            gl.enable_vertex_attrib_array(0);
            gl.vertex_attrib_pointer_f32(0, 3, glow::FLOAT, false, 3 * 4, 0);

            let cube_vbo_normals = gl.create_buffer().ok()?;
            gl.bind_buffer(glow::ARRAY_BUFFER, Some(cube_vbo_normals));
            gl.buffer_data_u8_slice(
                glow::ARRAY_BUFFER,
                bytemuck::cast_slice(&cube_normals),
                glow::STATIC_DRAW,
            );
            gl.enable_vertex_attrib_array(1);
            gl.vertex_attrib_pointer_f32(1, 3, glow::FLOAT, false, 3 * 4, 0);

            let cube_ebo = gl.create_buffer().ok()?;
            gl.bind_buffer(glow::ELEMENT_ARRAY_BUFFER, Some(cube_ebo));
            gl.buffer_data_u8_slice(
                glow::ELEMENT_ARRAY_BUFFER,
                bytemuck::cast_slice(&cube_indices),
                glow::STATIC_DRAW,
            );

            let cube_index_count = cube_indices.len() as i32;
            gl.bind_vertex_array(None);

            // Build the internal grid lines: subdividing the cube
            let step = largest_dim / (cubes_per_dim as f32);

            let x0 = cube_min_x;
            let y0 = cube_min_y;
            let z0 = cube_min_z;

            let x1 = cube_max_x; // x0 + step * cubes_per_dim
            let y1 = cube_max_y; // y0 + ...
            let z1 = cube_max_z;

            let mut grid_positions = Vec::new();
            let mut grid_normals = Vec::new();
            let mut grid_indices = Vec::new();

            // Helper to push a vertex (0-normal)
            let mut push_vertex = |x: f32, y: f32, z: f32| {
                let idx = (grid_positions.len() / 3) as u32;
                grid_positions.push(x);
                grid_positions.push(y);
                grid_positions.push(z);
                grid_normals.extend_from_slice(&[0.0, 0.0, 0.0]);
                idx
            };

            // For an NxN subdivision, we have (N+1) lines in each dimension
            let _n_plus_1 = cubes_per_dim + 1;

            // Lines parallel to X
            for iy in 0..=cubes_per_dim {
                let y = y0 + (iy as f32) * step;
                for iz in 0..=cubes_per_dim {
                    let z = z0 + (iz as f32) * step;
                    let start = push_vertex(x0, y, z);
                    let end = push_vertex(x1, y, z);
                    grid_indices.push(start);
                    grid_indices.push(end);
                }
            }

            // Lines parallel to Y
            for ix in 0..=cubes_per_dim {
                let x = x0 + (ix as f32) * step;
                for iz in 0..=cubes_per_dim {
                    let z = z0 + (iz as f32) * step;
                    let start = push_vertex(x, y0, z);
                    let end = push_vertex(x, y1, z);
                    grid_indices.push(start);
                    grid_indices.push(end);
                }
            }

            // Lines parallel to Z
            for ix in 0..=cubes_per_dim {
                let x = x0 + (ix as f32) * step;
                for iy in 0..=cubes_per_dim {
                    let y = y0 + (iy as f32) * step;
                    let start = push_vertex(x, y, z0);
                    let end = push_vertex(x, y, z1);
                    grid_indices.push(start);
                    grid_indices.push(end);
                }
            }

            // Build VAO for the grid
            let grid_vao = gl.create_vertex_array().ok()?;
            gl.bind_vertex_array(Some(grid_vao));

            let grid_vbo_positions = gl.create_buffer().ok()?;
            gl.bind_buffer(glow::ARRAY_BUFFER, Some(grid_vbo_positions));
            gl.buffer_data_u8_slice(
                glow::ARRAY_BUFFER,
                bytemuck::cast_slice(&grid_positions),
                glow::STATIC_DRAW,
            );
            gl.enable_vertex_attrib_array(0);
            gl.vertex_attrib_pointer_f32(0, 3, glow::FLOAT, false, 3 * 4, 0);

            let grid_vbo_normals = gl.create_buffer().ok()?;
            gl.bind_buffer(glow::ARRAY_BUFFER, Some(grid_vbo_normals));
            gl.buffer_data_u8_slice(
                glow::ARRAY_BUFFER,
                bytemuck::cast_slice(&grid_normals),
                glow::STATIC_DRAW,
            );
            gl.enable_vertex_attrib_array(1);
            gl.vertex_attrib_pointer_f32(1, 3, glow::FLOAT, false, 3 * 4, 0);

            let grid_ebo = gl.create_buffer().ok()?;
            gl.bind_buffer(glow::ELEMENT_ARRAY_BUFFER, Some(grid_ebo));
            gl.buffer_data_u8_slice(
                glow::ELEMENT_ARRAY_BUFFER,
                bytemuck::cast_slice(&grid_indices),
                glow::STATIC_DRAW,
            );

            let grid_index_count = grid_indices.len() as i32;
            gl.bind_vertex_array(None);

            Some(Self {
                program,

                // STL
                stl_vao,
                stl_vbo_positions,
                stl_vbo_normals,
                stl_ebo,
                stl_index_count,

                // bounding "cube"
                cube_vao,
                cube_vbo_positions,
                cube_vbo_normals,
                cube_ebo,
                cube_index_count,

                // internal grid
                grid_vao,
                grid_vbo_positions,
                grid_vbo_normals,
                grid_ebo,
                grid_index_count,

                u_mvp_loc,
                u_use_normals_loc,
            })
        }
    }

    pub fn paint(&self, gl: &glow::Context, mvp: [f32; 16]) {
        unsafe {
            gl.use_program(Some(self.program));

            if let Some(loc) = &self.u_mvp_loc {
                gl.uniform_matrix_4_f32_slice(Some(loc), false, &mvp);
            }

            if let Some(loc) = &self.u_use_normals_loc {
                gl.uniform_1_i32(Some(loc), 1); // useNormals = true
            }
            gl.bind_vertex_array(Some(self.stl_vao));
            gl.draw_elements(glow::TRIANGLES, self.stl_index_count, glow::UNSIGNED_INT, 0);

            if let Some(loc) = &self.u_use_normals_loc {
                gl.uniform_1_i32(Some(loc), 0); // useNormals = false
            }
            gl.line_width(2.0);
            gl.bind_vertex_array(Some(self.cube_vao));
            gl.draw_elements(glow::LINES, self.cube_index_count, glow::UNSIGNED_INT, 0);

            gl.line_width(1.0);
            gl.bind_vertex_array(Some(self.grid_vao));
            gl.draw_elements(glow::LINES, self.grid_index_count, glow::UNSIGNED_INT, 0);

            gl.bind_vertex_array(None);
        }
    }

    pub fn destroy(&self, gl: &glow::Context) {
        unsafe {
            // STL
            gl.delete_buffer(self.stl_vbo_positions);
            gl.delete_buffer(self.stl_vbo_normals);
            gl.delete_buffer(self.stl_ebo);
            gl.delete_vertex_array(self.stl_vao);

            // bounding cube
            gl.delete_buffer(self.cube_vbo_positions);
            gl.delete_buffer(self.cube_vbo_normals);
            gl.delete_buffer(self.cube_ebo);
            gl.delete_vertex_array(self.cube_vao);

            // grid
            gl.delete_buffer(self.grid_vbo_positions);
            gl.delete_buffer(self.grid_vbo_normals);
            gl.delete_buffer(self.grid_ebo);
            gl.delete_vertex_array(self.grid_vao);

            // shader program
            gl.delete_program(self.program);
        }
    }
}

pub struct StlRenderTab {
    gl: Option<Arc<glow::Context>>,
    model: Option<Arc<Mutex<StlModel>>>,

    angle_x: f32,
    angle_y: f32,
    offset_x: f32,
    offset_y: f32,
    scale: f32,
}

impl StlRenderTab {
    pub fn new(gl: &Arc<glow::Context>, stl_path: &str, cubes_per_dim: u32) -> Self {
        let model = StlModel::new(gl, stl_path, cubes_per_dim).map(|m| Arc::new(Mutex::new(m)));
        Self {
            gl: Some(gl.clone()),
            model,
            angle_x: 0.0,
            angle_y: 0.0,
            offset_x: 0.0,
            offset_y: 0.0,
            scale: 0.01,
        }
    }
}

impl DockTab for StlRenderTab {
    fn title(&self) -> &str {
        "STL w/ Centered Cube"
    }

    fn ui(&mut self, ui: &mut egui::Ui) {
        if let (Some(_gl), Some(model_arc)) = (&self.gl, &self.model) {
            let (rect, response) = ui.allocate_exact_size(
                egui::Vec2::splat(500.0),
                egui::Sense::drag(), // so we can detect mouse drags
            );

            // Mouse-driven transformations
            if response.dragged() {
                let delta = response.drag_delta();
                let modifiers = ui.ctx().input(|i| i.modifiers);
                // SHIFT for zoom, CTRL for pan, otherwise rotate
                if modifiers.shift_only() {
                    // Zoom
                    self.scale *= 1.0 + (delta.y * -0.01);
                    self.scale = self.scale.max(1e-5).min(10.0);
                } else if modifiers.ctrl {
                    // Pan
                    self.offset_x += delta.x * 0.5;
                    self.offset_y -= delta.y * 0.5; // invert Y if you prefer
                } else if !modifiers.alt && !modifiers.command && !modifiers.mac_cmd {
                    // No shift/ctrl: rotate
                    self.angle_x += delta.y * 0.01;
                    self.angle_y += delta.x * 0.01;
                }
            }

            ui.label(format!(
                "Drag inside the 500×500 region:\n\
                 - Rotate (no modifier)\n\
                 - Pan (CTRL)\n\
                 - Zoom (SHIFT)\n\
                 \nAngles: {:.2}, {:.2}, Offset: ({:.2}, {:.2}), Scale: {:.4}",
                self.angle_x, self.angle_y, self.offset_x, self.offset_y, self.scale,
            ));

            let model_clone = model_arc.clone();

            let angle_x = self.angle_x;
            let angle_y = self.angle_y;
            let offset_x = self.offset_x;
            let offset_y = self.offset_y;
            let scale = self.scale;

            let cb = egui_glow::CallbackFn::new(move |_info, painter| {
                let gl = painter.gl();
                let model = model_clone.lock();

                unsafe {
                    // gl.clear_color(0.2, 0.2, 0.2, 1.0);
                    // gl.clear(glow::COLOR_BUFFER_BIT | glow::DEPTH_BUFFER_BIT);
                    gl.enable(glow::DEPTH_TEST);
                    gl.enable(glow::BLEND);
                    gl.blend_func(glow::SRC_ALPHA, glow::ONE_MINUS_SRC_ALPHA);
                }

                let mvp = make_mvp(angle_x, angle_y, offset_x, offset_y, scale);
                model.paint(gl, mvp);
            });

            ui.painter().add(egui::PaintCallback {
                rect,
                callback: Arc::new(cb),
            });
        }
    }

    fn closeable(&mut self) -> bool {
        false
    }
}

impl Drop for StlRenderTab {
    fn drop(&mut self) {
        if let (Some(gl), Some(model)) = (&self.gl, &self.model) {
            model.lock().destroy(&gl);
        }
    }
}

/// Compose rotation, translation, and scale into a single 4x4 matrix
///
/// We'll do M = Translation * RotationY(angle_y) * RotationX(angle_x) * Scale
fn make_mvp(ax: f32, ay: f32, tx: f32, ty: f32, scale: f32) -> [f32; 16] {
    // 1) Scale
    let s = [
        scale, 0.0,   0.0,   0.0,
        0.0,   scale, 0.0,   0.0,
        0.0,   0.0,   scale, 0.0,
        0.0,   0.0,   0.0,   1.0,
    ];

    // 2) Rotation X
    let (sx, cx) = ax.sin_cos();
    let rx = [
        1.0, 0.0, 0.0, 0.0,
        0.0, cx,  -sx, 0.0,
        0.0, sx,   cx, 0.0,
        0.0, 0.0,  0.0, 1.0,
    ];

    // 3) Rotation Y
    let (sy, cy) = ay.sin_cos();
    let ry = [
        cy,  0.0, sy,  0.0,
        0.0, 1.0, 0.0, 0.0,
        -sy, 0.0, cy,  0.0,
        0.0, 0.0, 0.0, 1.0,
    ];

    // 4) Translation
    // We'll add a negative z-offset (e.g. -5.0) so geometry is in front of the camera
    let t = [
        1.0, 0.0, 0.0, 0.0,
        0.0, 1.0, 0.0, 0.0,
        0.0, 0.0, 1.0, 0.0,
        tx,  ty,  -5.0, 1.0,
    ];

    let m = mat4_mul(t, ry);
    let m = mat4_mul(m, rx);
    mat4_mul(m, s)
}


/// Simple 4x4 float matrix multiply (row-major)
fn mat4_mul(a: [f32; 16], b: [f32; 16]) -> [f32; 16] {
    let mut r = [0.0; 16];
    for row in 0..4 {
        for col in 0..4 {
            let mut sum = 0.0;
            for k in 0..4 {
                sum += a[row * 4 + k] * b[k * 4 + col];
            }
            r[row * 4 + col] = sum;
        }
    }
    r
}
