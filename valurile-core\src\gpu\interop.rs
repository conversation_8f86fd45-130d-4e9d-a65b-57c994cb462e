use cudarc::driver::sys::CUgraphicsRegisterFlags_enum::CU_GRAPHICS_REGISTER_FLAGS_NONE;
use cudarc::driver::sys::CUresult::CUDA_SUCCESS;
use cudarc::driver::sys::{CUdeviceptr, CUgraphicsResource, CUresult};
use glow::{Context, HasContext as _};
use std::ptr;

// Manual extern for CUDA-OpenGL interop
pub mod cuda_gl_interop {
    use std::os::raw::c_uint;
    #[link(name = "cuda")]
    extern "C" {
        pub fn cuGraphicsGLRegisterBuffer(
            pResource: *mut super::CUgraphicsResource,
            buffer: c_uint,
            flags: c_uint,
        ) -> super::CUresult;
    }
}

/// CUDA-OpenGL interop resource manager
pub struct CudaGLInterop {
    pub pbo: glow::Buffer,
    pub cuda_resource: CUgraphicsResource,
    size_bytes: usize,
}

impl CudaGLInterop {
    /// Create a new CUDA-OpenGL interop buffer
    pub fn new(gl: &Context, size_bytes: usize) -> Result<Self, String> {
        unsafe {
            // Create PBO in OpenGL
            let pbo = gl.create_buffer()
                .map_err(|_| "Failed to create PBO")?;

            gl.bind_buffer(glow::PIXEL_UNPACK_BUFFER, Some(pbo));
            gl.buffer_data_size(
                glow::PIXEL_UNPACK_BUFFER,
                size_bytes as i32,
                glow::DYNAMIC_DRAW,
            );
            gl.bind_buffer(glow::PIXEL_UNPACK_BUFFER, None);

            // Register with CUDA
            let raw_id: u32 = std::mem::transmute(pbo);
            let mut cuda_resource: CUgraphicsResource = ptr::null_mut();

            let status = cuda_gl_interop::cuGraphicsGLRegisterBuffer(
                &mut cuda_resource as *mut _,
                raw_id,
                CU_GRAPHICS_REGISTER_FLAGS_NONE as u32,
            );

            if status != CUDA_SUCCESS {
                gl.delete_buffer(pbo);
                return Err(format!("Failed to register GL buffer with CUDA: {:?}", status));
            }

            Ok(Self {
                pbo,
                cuda_resource,
                size_bytes,
            })
        }
    }

    /// Map the resource for CUDA access and return device pointer
    pub fn map_for_cuda(&mut self) -> Result<CUdeviceptr, String> {
        // TODO: Update this to use the new cudarc API
        // For now, return an error to indicate this functionality needs updating
        Err("CUDA-OpenGL interop mapping not yet updated for cudarc 0.16.4".to_string())
    }

    /// Unmap the resource after CUDA operations
    pub fn unmap_from_cuda(&mut self) -> Result<(), String> {
        // TODO: Update this to use the new cudarc API
        // For now, return an error to indicate this functionality needs updating
        Err("CUDA-OpenGL interop unmapping not yet updated for cudarc 0.16.4".to_string())
    }

    /// Get the OpenGL buffer for texture operations
    pub fn gl_buffer(&self) -> glow::Buffer {
        self.pbo
    }

    /// Clean up resources
    pub fn destroy(&mut self, gl: &Context) {
        unsafe {
            // TODO: Update this to use the new cudarc API for unregistering CUDA resources
            // For now, just clean up the OpenGL buffer
            gl.delete_buffer(self.pbo);
        }
    }
}

/// High-level LBM visualization interop
pub struct LBMVisualizationInterop {
    pub interop: CudaGLInterop,
    pub texture: glow::Texture,
    pub width: u32,
    pub height: u32,
}

impl LBMVisualizationInterop {
    /// Create a new LBM visualization interop system
    pub fn new(gl: &Context, width: u32, height: u32) -> Result<Self, String> {
        let size_bytes = (width * height * std::mem::size_of::<f32>() as u32) as usize;
        let interop = CudaGLInterop::new(gl, size_bytes)?;

        // Create texture for visualization
        let texture = crate::gpu::opengl::TextureManager::create_velocity_texture(gl, width, height)?;

        Ok(Self {
            interop,
            texture,
            width,
            height,
        })
    }

    /// Execute CUDA kernel to write visualization data, then update texture
    pub fn compute_and_update<F>(&mut self, gl: &Context, cuda_kernel: F) -> Result<(), String>
    where
        F: FnOnce(CUdeviceptr) -> Result<(), String>,
    {
        // Map for CUDA access
        let cuda_ptr = self.interop.map_for_cuda()?;

        // Execute the CUDA kernel
        cuda_kernel(cuda_ptr)?;

        // Unmap from CUDA
        self.interop.unmap_from_cuda()?;

        // Update texture from PBO
        crate::gpu::opengl::TextureManager::update_from_pbo(
            gl,
            self.texture,
            self.interop.gl_buffer(),
            self.width,
            self.height,
        );

        Ok(())
    }

    /// Bind texture for rendering
    pub fn bind_texture(&self, gl: &Context, texture_unit: u32) {
        unsafe {
            gl.active_texture(glow::TEXTURE0 + texture_unit);
            gl.bind_texture(glow::TEXTURE_2D, Some(self.texture));
        }
    }

    /// Clean up all resources
    pub fn destroy(&mut self, gl: &Context) {
        unsafe {
            gl.delete_texture(self.texture);
        }
        self.interop.destroy(gl);
    }
}