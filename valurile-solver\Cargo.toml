[package]
name = "valurile-solver"
version = "0.1.0"
edition = "2021"

[dependencies]
clap = { workspace = true }
colored = "2.1.0"
cust = "0.3.2"
indicatif = "0.17.8"
ndarray = { version = "0.16.1", features = ["rayon"] }
ndarray-rand = "0.15.0"
num_cpus = "1.16.0"
ocl = "0.19.7"
opencl3 = "0.9.5"
rand = "0.8.5"
rayon = "1.10.0"
rustacuda = "0.1.3"
vtkio = "0.6.3"
cudarc = { workspace = true }
glow = { workspace = true }

[build-dependencies]
cc = "1.0"
